import time
from asyncio import TaskGroup
from typing import List

from core.schema.chat_request import ChatRequest
from core.schema.chat_base import MessageType
from service.base_service import BaseService
from util.common_util import is_empty
from util.string_util import normalize_item_name

PRODUCT_DESCRIBE = """列表给出的产品都是手机（phone）产品，其中redmi的中文名称‘红米’；xiaomi的中文名称‘小米’；‘+’的另一种表述是‘plus’；未明确指出产品型号为 ‘5g’ 的产品，默认该产品型号为 ‘4g’"""
FIRST_TAG_LABELS = ["和其他机型的对比", "设计理由", "硬件或系统配置", "软件操作", "销售信息"]

SECOND_TAG_DICT = {
    "接口": "Port",
    "导航与定位": "Navigasi & Penentuan Posisi",
    "视频": "Pemutaran video",
    "内存与存储": "RAM dan Kapasitas Penyimpanan",
    "网络": "<PERSON>aringan dan Konektivitas",
    "音频": "Pemutaran audio",
    "电池与充电": "Baterai dan Pengisian Daya",
    "用户界面与系统": "Antarmuka pengguna dan sistem",
    "屏幕": "Layar",
    "包装内容": "Termasuk dalam kotak kemasan",
    "解锁": "Buka kunci",
    "传感器": "Sensor",
    "安全与认证": "Keamanan & Autentikasi",
    "指纹传感器与按钮": "Sensor sidik jari dan Tombol",
    "外观": "Tampilan",
    "功能": "Fitur",
    "冷却系统": "Sistem pendinginan",
    "设计": "Desain",
    "性能": "Performa",
    "尺寸": "Dimensi",
    "后置摄像头": "Kamera Belakang",
    "前置摄像头": "Kamera Depan",
    "振动电机": "Motor getaran",
    "开机方法": "Cara membuka ponsel",
    "NFC": "NFC",
    "防水与防尘": "Tahan dari Percikan, Air, dan Debu, IP rating",
    "操作系统": "Sistem Operasi",
    "AI功能": "Fitur AI",
    "处理器": "Chipset",
    "多媒体": "Multimedia",
    "相机": "Kamera",
}


class QueryParseService(BaseService):
    def __init__(self,
                 logger,
                 request_receive_time,
                 api_key_json,  # old api key for tag_question_second
                 api_key_json2,  # new api key for recognize_item_names
                 api_key_json3,  # new api key for tag_question_first
                 model_manager,
                 item_name_list,
                 normalized_item_name_list,
                 item_name_xiaomi_list):

        super().__init__(logger, None, None, request_receive_time)
        self._api_key_json = api_key_json
        self._api_key_json2 = api_key_json2
        self._api_key_json3 = api_key_json3
        self._model_manager = model_manager
        self._item_name_list = item_name_list
        self._normalized_item_name_list = normalized_item_name_list
        self._item_name_xiaomi_list = item_name_xiaomi_list

    async def filter_by_first_tags_430(self, chat_request: ChatRequest, chat_request_id, tg: TaskGroup):
        # TODO(jxy) 仅用于430版本的支持，后续将要删去
        # 这两个不适合放在一个 prompt 因为他们会相互干扰：打一级标签的时候会提示「当前的问题是针对 xxx」，但这个会影响实体的提取
        item_name_chunks = self.chunked(self._item_name_list, 50)
        recognize_item_names_tasks = []
        for chunk in item_name_chunks:
            task = tg.create_task(self.recognize_item_names_exact(
                chat_request.ending_message, chat_request_id, chunk
            ))
            recognize_item_names_tasks.append(task)
        tag_question_first_task = tg.create_task(self.tag_question_first(
            chat_request.ending_message, chat_request.item_name, chat_request_id
        ))
        actual_item_names = []
        total_tokens_recognize_item_names = 0
        for task in recognize_item_names_tasks:
            batch_actual_item_names, batch_total_tokens = await task
            actual_item_names.extend(batch_actual_item_names)
            total_tokens_recognize_item_names += batch_total_tokens
        first_tags_normalized, total_tokens_tag_question_first, _ = await tag_question_first_task
        self._logger.info(f"selected_item_name: {chat_request.item_name}")
        self._logger.info(
            f"模型推理结果: actual_item_names===> {actual_item_names}; first tag===> {first_tags_normalized}"
        )

        is_not_in_scope, _ = self.should_filter_by_first_tags(first_tags_normalized)
        is_dynamic_change, _ = self.is_dynamic_change_tags(first_tags_normalized)

        query_filter_result = MessageType.TEXT
        if is_not_in_scope:
            query_filter_result = MessageType.NOT_IN_SCOPE

        if is_dynamic_change:
            query_filter_result = MessageType.DYNAMIC_CHANGE

        if not self._match_selected_item_names(chat_request.item_name_normalized, actual_item_names):
            query_filter_result = MessageType.NOT_MATCH_ITEM
        self.log_elapse("根据一级标签进行过滤返回")
        total_tokens_filter_by_first_tags = total_tokens_tag_question_first + total_tokens_recognize_item_names
        return query_filter_result, actual_item_names, first_tags_normalized, total_tokens_filter_by_first_tags

    async def recognize_item_names(self, chat_request: ChatRequest, chat_request_id, is_fuzzy=False):
        total_tokens_recognize_item_names = 0
        item_name_chunks = self.chunked(self._item_name_list, 50)
        async with TaskGroup() as tg:
            recognize_exact_item_names_tasks = []
            for chunk in item_name_chunks:
                task = tg.create_task(self.recognize_item_names_exact(
                    chat_request.ending_message, chat_request_id, chunk
                ))
                recognize_exact_item_names_tasks.append(task)
            exact_item_names = []
            for task in recognize_exact_item_names_tasks:
                batch_exact_item_names, batch_total_tokens = await task
                exact_item_names.extend(batch_exact_item_names)
                total_tokens_recognize_item_names += batch_total_tokens

            if is_fuzzy:
                recognize_fuzzy_item_names_tasks = []
                for chunk in item_name_chunks:
                    task = tg.create_task(self.recognize_item_names_fuzzy(
                        chat_request.ending_message, chat_request_id, exact_item_names, chunk
                    ))
                    recognize_fuzzy_item_names_tasks.append(task)
                fuzzy_item_names = []
                for task in recognize_fuzzy_item_names_tasks:
                    batch_fuzzy_item_names, batch_total_tokens = await task
                    fuzzy_item_names.extend(batch_fuzzy_item_names)
                    total_tokens_recognize_item_names += batch_total_tokens

                if len(set(fuzzy_item_names) - set(exact_item_names)) > 0:
                    fuzzy_result = True
                else:
                    fuzzy_result = False
                fuzzy_item_names_with_exact = list(set(exact_item_names + fuzzy_item_names))
                self._logger.debug(f'exact_item_names: {exact_item_names}')
                self._logger.debug(f'fuzzy_item_names: {fuzzy_item_names}')
                self._logger.debug(f'fuzzy_item_names_with_exact: {fuzzy_item_names_with_exact}')
                return fuzzy_result, exact_item_names, fuzzy_item_names, fuzzy_item_names_with_exact, total_tokens_recognize_item_names

        return exact_item_names, total_tokens_recognize_item_names

    async def recognize_item_names_for_chunk(self, chat_request: ChatRequest, chat_request_id, tg: TaskGroup,
                                             mode='exact'):
        start_time = time.time()
        # 由于530版本的模糊识别仅用于问答类，而不用于双机对比类
        # 故，模糊识别机型仅会用到xiaomi的机型
        if mode == 'exact':
            item_name_chunks = self.chunked(self._item_name_list, 100)
        elif mode == 'fuzzy':
            item_name_chunks = self.chunked(self._item_name_xiaomi_list, 100)
        else:
            raise NotImplementedError
        recognize_item_names_tasks = []
        item_names = []
        total_tokens_recognize_item_names = 0

        for chunk in item_name_chunks:
            if mode == 'exact':
                recognize_task = tg.create_task(self.recognize_item_names_exact(
                    chat_request.ending_message, chat_request_id, chunk
                ))
            elif mode == 'fuzzy':
                recognize_task = tg.create_task(self.recognize_item_names_fuzzy(
                    chat_request.ending_message, chat_request_id, chunk
                ))
            else:
                raise NotImplementedError
            recognize_item_names_tasks.append(recognize_task)

        for task in recognize_item_names_tasks:
            batch_item_names, batch_total_tokens = await task
            item_names.extend(batch_item_names)
            total_tokens_recognize_item_names += batch_total_tokens

        return item_names, total_tokens_recognize_item_names, start_time

    async def recognize_tags(self, question_cn, chat_request_id):
        start_time = time.time()
        label_part = '\n'.join(SECOND_TAG_DICT.keys())
        prompt = f"""请对用户关于手机的提问进行分析，并判断是否涉及以下内容，
{label_part}
严格用 json 列表形式返回相关的标签，不要添加其他内容，例如：
用户提问：可以无线充电吗？
回答：{{"提问标签":["电池与充电"]}}
---
以下是用户对手机的提问：{question_cn}
回答：
"""
        is_success, response, total_tokens_recognize_tage = await self._model_manager.call_llm_with_json(prompt,
                                                                                                         self._api_key_json,
                                                                                                         chat_request_id)
        second_tag_map_dict = dict()
        if not is_success:
            return second_tag_map_dict, total_tokens_recognize_tage, start_time

        second_tag_list = []
        if "提问标签" in response:
            second_tag_list = response["提问标签"]
        second_tag_map_dict = dict()
        for second_tag in second_tag_list:
            if second_tag in SECOND_TAG_DICT:
                second_tag_map_dict[second_tag] = SECOND_TAG_DICT[second_tag]
        return second_tag_map_dict, total_tokens_recognize_tage, start_time

    async def extract_item_names(self, question_cn, chat_request_id):
        start_time = time.time()
        prompt = f"""你是一个专业的数据标注专家，下面将给你一段买家对产品的提问内容，你需要根据你的专业知识，提取出这段提问query所涉及的产品类型。
对于产品类型的说明：{PRODUCT_DESCRIBE}
你要提取出买家提及的所有的产品类型（如果有，则必须给出完整的列表），如果买家提及的产品不明确则给出“未知产品类型”。
下面是给出的一个例子：
【query】：'Redmi Note 14 Pro 5G 的电池耐用吗？'
【回复】：{ {"产品类型": ["Redmi Note 14 Pro 5G"]} }
【query】：'baterai 5110 mAh dengan pengisian 120 W hypercharge'
【回复】：{ {"产品类型": ["未知产品类型"]} }
你的回复内容必须严格遵循json格式，其中‘产品类型’的value必须是个list列表。
只需要给出回复内容（产品类型）即可，不需要复述query，不要输出额外的内容
注意：你提取的内容必须严格和query中的语言保持一致，例如，query的语言是中文，那你必须提取的是中文
【query】: {question_cn}
【回复】:
"""
        is_success, response, total_tokens_extract_item_names = await self._model_manager.call_llm_with_json(
            prompt, self._api_key_json2,
            chat_request_id)
        if not is_success:
            return list(), total_tokens_extract_item_names, start_time
        product_codes = self._get_product_codes_from_response_not_in_candicates(response, "产品类型")
        self._logger.debug(f'extracted_item_names: {product_codes}')
        return product_codes, total_tokens_extract_item_names, start_time

    async def recognize_item_names_exact(self, question_cn, chat_request_id, item_name_list):
        prompt = f"""你是一个专业的数据标注专家，下面将给你一段买家对产品的提问内容，你需要根据你的专业知识，判断这段提问query所涉及的产品类型。
说明：产品类型中有很多对产品的迭代升级，因此会有很多额外的后缀，比如“pro”、“pro+”，你在识别产品类型时，应该遵循最短匹配原则
注意：你给出的结果必须时精准无误的，不要猜测，不要联想，必须给出有把握的产品类型
你可以选择的产品类型有：{",".join(item_name_list)}
对于产品类型的说明：{PRODUCT_DESCRIBE}
你要判断买家提及的产品属于给出的产品列表中的哪一类或哪几类（如果有，则必须给出完整的列表），如果买家提及的产品不明确或者不属于产品列表中的任何一类，则给出“未知产品类型”。
下面是给出的一个例子：
【query】：'Redmi Note 14 Pro 5G 的电池耐用吗？'
【回复】：{ {"产品类型": ["Redmi Note 14 Pro 5G"]} }
【query】：'baterai 5110 mAh dengan pengisian 120 W hypercharge'
【回复】：{ {"产品类型": ["未知产品类型"]} }
【query】：'Redmi note13 apakah bagus digunakan?'
【回复】：{ {"产品类型": ["Redmi Note 13"]} }
你的回复内容必须严格遵循json格式，其中‘产品类型’的value必须是个list列表。
只需要给出回复内容（产品类型）即可，不需要复述query，不要输出额外的内容
【query】: {question_cn}
【回复】:
"""
        is_success, response, total_tokens_recognize_item_names = await self._model_manager.call_llm_with_json(
            prompt, self._api_key_json2,
            chat_request_id)
        if not is_success:
            return list(), total_tokens_recognize_item_names

        product_codes = self._get_product_codes_from_response(response, "产品类型", item_name_list)
        return product_codes, total_tokens_recognize_item_names

    async def recognize_item_names_fuzzy(self, question_cn, chat_request_id, item_name_list):
        prompt = f"""你是一个专业的数据标注专家，下面将给你一段买家对产品的提问内容以及已经识别出来的涉及到的产品类型，你需要根据你的专业知识，猜测下买家可能提及的产品类型.
说明:由于买家提及的产品类型可能仅是产品名称的简称、别称、名称的一部分,因此,你需要有一定的猜测和联想,仅根据买家提及的部分名称猜测下可能设计的产品名称
注意：你的猜测仅限于买家给出的产品名称不完整、存在歧义的时候，才要给出猜测，否则，不要随意额外的猜测
你可以选择的产品类型有：{",".join(item_name_list)}
对于产品类型的说明：{PRODUCT_DESCRIBE}
你要判断买家提及的产品属于给出的产品列表中的哪一类或哪几类（如果有，则必须给出完整的列表），如果买家提及的产品不明确或者不属于产品列表中的任何一类，则给出“未知产品类型”。
下面是给出的一个例子：
【query】：'Redmi Note 14 Pro 5G 的电池耐用吗？'
【回复】：{{"猜测可能提及的产品类型": ["Redmi Note 14 Pro 5G"]}}
【query】：'baterai 5110 mAh dengan pengisian 120 W hypercharge'
【回复】：{{"猜测可能提及的产品类型": []}}
【query】：'15 的屏幕和vivo x200 pro哪个更好?'
【回复】：{{"猜测可能提及的产品类型": ["VIVO X200 Pro", "Xiaomi 15", "iPhone 15"]}}
你的回复内容必须严格遵循json格式
只需要给出回复内容即可，不需要复述query，不要输出额外的内容
【query】: {question_cn}
【回复】:
"""
        is_success, response, total_tokens_recognize_item_names = await self._model_manager.call_llm_with_json(
            prompt, self._api_key_json2,
            chat_request_id)
        if not is_success:
            return list(), total_tokens_recognize_item_names

        product_codes = self._get_product_codes_from_response(response, "猜测可能提及的产品类型", item_name_list)
        return product_codes, total_tokens_recognize_item_names

    async def tag_question_first(self, question_cn, product_code, chat_request_id):
        start_time = time.time()
        if product_code is None or product_code == "":
            prompt = f"""你是一个专业的数据标注专家，下面将给你一段买家对手机产品的提问内容，你需要根据你的专业知识，判断这段提问query所属的类别。
可以选择的类别有：
1. 规格参数，咨询手机产品的硬件或系统配置以及其他和规格参数、性能相关问题，包括但不限于手机产品的外观、尺寸、内存与存储、处理器/芯片/跑分等性能相关的配置、支持的网络类型、音频相关的配置、传感器、冷却方式、屏幕、相机（前置、后置摄像头）、充电器（充电和耗电速度等）、接口类型（是否有type-c充电口、耳机孔等）、导航与定位、是否有指纹解锁、是否支持NFC、使用的是什么操作系统、防水、防尘、防摔等级等和手机产品硬件以及系统配置有关的信息。比如：Redmi 13 有超广角镜头吗？能带得动原神吗？
2. 软件使用，用户咨询手机软件操作等相关的问题，比如：如何在观看视频时去除 Note 14 Pro 5G上的广告？Note 14 支持双视频吗？
3. 卖点咨询，手机产品卖点/优势相关问题，比如：有哪些卖点？卖点是什么？xiaomi 15 有哪些功能？按fabe原则介绍xiaomi 15
4. 缺点咨询，必须是明确的提及手机产品的某个方面存在什么缺点或吐槽，比如：手机使用起来发热吗？看视频卡顿吗？Redmi 13 充电容易发热、Xiaomi 14T视频为何这么卡顿？为什么不支持超广角？不包括模糊、笼统的问有哪些缺点
5. 双机对比，和其他机型/手机产品的对比，比如：redmi 13和之前的版本有什么改进和优化，redmi 13和其他产品有什么区别和亮点等，需要注意的是，必须能够表现出买家希望得到的“对比”、“区别”、“差异化”；
6. 数码知识，主要指的是和3c数码产品有关的、通识类信息，注意：没有明确的在询问该产品的信息，而是询问通用的3c数码产品的通用知识，比如：闪存和内存有什么区别？
7. 售前售后咨询，指的是和销售相关的售前售后信息，包括但不限于销售信息，和销售服务、售后服务有关的信息，比如售卖价格、保修要求、保修期限、售后维修、快递服务等，不包括产品的卖点、优势、区别、以及购买该产品的理由和考虑等与售前、售后等销售无关的信息. 比如：xiaomi 15 多少钱？xiaomi 15 Ultra 为何库存这么少？
8. 时效性问题：指未发生或答案随时间推移而变化的问题，如：小米什么时候上市新款手机？苹果哪款手机卖得最好？
9. 闲聊对话，属于非手机产品的提问的一种，提问内容与手机产品完全无关，主要指闲聊话题，比如：你是谁、你好、谢谢、我男朋友走丢了，你能帮我找找吗？
10. 模糊意图，不完整或无意义的输入，或者笼统地询问手机产品有哪些缺点等，比如：有什么缺点、哪个、- 87*%￥
11. 其他意图，不属于上述所有类别之外的，包括但不限于文本创作、算数计算、图片生成、翻译、图片搜索、连接查询、代码生成等，比如：给我讲个笑话、帮我写首诗、图片、小米15 Ultra的示例照片、查看激活 865314072321705等
注意：这是一个多选题，你需要标注的query可能涉及给出的这几个类别中的一个或多个，你需要依次判断这个query是否属于针对该类别的提问，你需要先思考，然后再给出结论。
说明：当query中仅有小米系列的产品时（包括但不限于xiaomi、redmi、poco），都默认买家在咨询“规格参数咨询”
下面是给出的一个例子：
【query】：'红米 Note 14 Pro 5G 的电池耐用吗？'
【回复】：{ {"query分类": ["规格参数咨询"]} }
【query】：'小米15有什么缺点？'
【回复】：{ {"query分类": ["模糊意图"]} }
你的回复内容必须严格遵循json格式，其中‘query分类’的value必须是个list列表。
只需要给出回复内容（query分类）即可，不需要复述query，不要输出额外的内容
【query】: {question_cn}
【回复】:
"""
        else:
            prompt = f"""你是一个专业的数据标注专家，下面将给你一段买家对手机产品 {product_code} 的提问内容，你需要根据你的专业知识，判断这段提问query所属的类别。
可以选择的类别有：
1. 规格参数，咨询手机产品 {product_code} 的硬件或系统配置以及其他和规格参数、性能相关问题，包括但不限于手机产品的外观、尺寸、内存与存储、处理器/芯片/跑分等性能相关的配置、支持的网络类型、音频相关的配置、传感器、冷却方式、屏幕、相机（前置、后置摄像头）、充电器（充电和耗电速度等）、接口类型（是否有type-c充电口、耳机孔等）、导航与定位、是否有指纹解锁、是否支持NFC、使用的是什么操作系统、防水、防尘、防摔等级等和手机产品硬件以及系统配置有关的信息。比如：Redmi 13 有超广角镜头吗？能带得动原神吗？
2. 软件使用，用户咨询手机软件操作等相关的问题，比如：如何在观看视频时去除 Note 14 Pro 5G上的广告？Note 14 支持双视频吗？
3. 卖点咨询，手机产品卖点/优势相关问题，比如：有哪些卖点？卖点是什么？xiaomi 15 有哪些功能？按fabe原则介绍xiaomi 15
4. 缺点咨询，必须是明确的提及手机产品的某个方面存在什么缺点或吐槽，比如：手机使用起来发热吗？看视频卡顿吗？Redmi 13 充电容易发热、Xiaomi 14T视频为何这么卡顿？为什么不支持超广角？不包括模糊、笼统的问有哪些缺点
5. 双机对比，和其他机型/手机产品的对比，比如：redmi 13和之前的版本有什么改进和优化，redmi 13和其他产品有什么区别和亮点等，需要注意的是，必须能够表现出买家希望得到的“对比”、“区别”、“差异化”；
6. 数码知识，主要指的是和3c数码产品有关的、通识类信息，注意：没有明确的在询问该产品的信息，而是询问通用的3c数码产品的通用知识，比如：闪存和内存有什么区别？
7. 售前售后咨询，指的是和销售相关的售前售后信息，包括但不限于销售信息，和销售服务、售后服务有关的信息，比如售卖价格、保修要求、保修期限、售后维修、快递服务等，不包括产品的卖点、优势、区别、以及购买该产品的理由和考虑等与售前、售后等销售无关的信息. 比如：xiaomi 15 多少钱？xiaomi 15 Ultra 为何库存这么少？
8. 时效性问题：指未发生或答案随时间推移而变化的问题，如：小米什么时候上市新款手机？苹果哪款手机卖得最好？
9. 闲聊对话，属于非手机产品的提问的一种，提问内容与手机产品完全无关，主要指闲聊话题，比如：你是谁、你好、谢谢、我男朋友走丢了，你能帮我找找吗？
10. 模糊意图，不完整或无意义的输入，或者笼统地询问手机产品有哪些缺点等，比如：有什么缺点、哪个、- 87*%￥
11. 其他意图，不属于上述所有类别之外的，包括但不限于文本创作、算数计算、图片生成、翻译、图片搜索、连接查询、代码生成等，比如：给我讲个笑话、帮我写首诗、图片、小米15 Ultra的示例照片、查看激活 865314072321705等
注意：这是一个多选题，你需要标注的query可能涉及给出的这几个类别中的一个或多个，你需要依次判断这个query是否属于针对该类别的提问，你需要先思考，然后再给出结论。
说明：当query中仅有小米系列的产品时（包括但不限于xiaomi、redmi、poco），都默认买家在咨询“规格参数咨询”
下面是给出的一个例子：
【手机产品】：redmi note 14 pro 5g
【query】：'红米 Note 14 Pro 5G 的电池耐用吗？'
【回复】：{ {"query分类": ["规格参数咨询"]} }
【query】：'小米15有什么缺点？'
【回复】：{ {"query分类": ["模糊意图"]} }
你的回复内容必须严格遵循json格式，其中‘query分类’的value必须是个list列表。
只需要给出回复内容（query分类）即可，不需要复述query，不要输出额外的内容
【手机产品】：{product_code}
【query】: {question_cn}
【回复】:
"""
        is_success, response, total_tokens_tag_question_first = await self._model_manager.call_llm_with_json(prompt,
                                                                                                             self._api_key_json3,
                                                                                                             chat_request_id)
        if not is_success:
            return "其他意图", total_tokens_tag_question_first, start_time

        self._logger.info(f"first tag promp===> {prompt}")
        self._logger.info(f"first tag response===> {response}")
        first_tags = self._get_first_tags_from_response(response)
        first_tags_normalized = list()
        for tag in first_tags:
            normalized = self._normalize_tag(tag)
            if normalized is None:
                self._logger.warning(f"大模型返回一个非法的 tag: {tag}")
                continue

            first_tags_normalized.append(normalized)
        if len(first_tags_normalized) == 0:
            first_tags_normalized = "其他意图"
        assert len(first_tags_normalized) > 0
        first_tags_normalized = self.get_first_intent_from_intends(first_tags_normalized)
        return first_tags_normalized, total_tokens_tag_question_first, start_time

    @staticmethod
    def chunked(iterable: List[str], n: int) -> List[List[str]]:
        """将列表分割为每组最多n个元素的子列表"""
        return [iterable[i:i + n] for i in range(0, len(iterable), n)]

    def _normalize_tag(self, tag):
        if "对比" in tag:
            tag = "双机对比"
        if "规格" in tag or "参数" in tag:
            tag = "规格参数"
        if "使用" in tag or "软件" in tag:
            tag = "软件使用"
        if "卖点" in tag:
            tag = "卖点咨询"
        if "缺点" in tag:
            tag = "缺点咨询"
        if "数码知识" in tag:
            tag = "数码知识"
        if "售前" in tag or "售后" in tag:
            tag = "售前售后咨询"
        if "时效" in tag:
            tag = "时效性问题"
        if "闲聊" in tag:
            tag = "闲聊对话"
        if "其他" in tag:
            tag = "其他意图"
        if "模糊" in tag:
            tag = "模糊意图"
        if tag not in ['规格参数', '软件使用', '卖点咨询', '缺点咨询',
                       '双机对比', '数码知识', '售前售后咨询',
                       '时效性问题', '闲聊对话', '其他意图', '模糊意图']:
            tag = "其他意图"
        return tag

    def get_first_intent_from_intends(self, intent_names):
        # 从意图列表中根据优先级，拿出优先级最高的意图
        # TODO(jxy) 待补充
        if "双机对比" in intent_names:
            return "双机对比"

        return intent_names[0]

    @staticmethod
    def should_filter_by_first_tags(normalized_tags):
        return is_empty(normalized_tags), "非手机咨询问题"

    @staticmethod
    def is_dynamic_change_tags(normalized_tags):
        if '销售信息' in normalized_tags:
            return True, "属于动态变化中的问题"

        return False, "不属于动态变化中的问题"

    def _match_selected_item_names(self, expected_item_name, actual_item_names):
        if is_empty(actual_item_names):
            return True

        for name in actual_item_names:
            if expected_item_name == normalize_item_name(name):
                return True

        return False

    def _get_product_codes_from_response(self, response, key_words, item_name_list):
        product_codes = []
        if key_words in response:
            if not isinstance(response[key_words], list):
                response[key_words] = [response[key_words]]
            for code in response[key_words]:
                if code == "未知产品类型":
                    continue
                if code not in item_name_list or normalize_item_name(code) not in self._normalized_item_name_list:
                    continue
                product_codes.append(code)
        return product_codes

    def _get_product_codes_from_response_not_in_candicates(self, response, key_words):
        product_codes = []
        if key_words in response:
            if not isinstance(response[key_words], list):
                response[key_words] = [response[key_words]]
            for code in response[key_words]:
                if code == "未知产品类型":
                    continue
                if normalize_item_name(code) not in self._normalized_item_name_list:
                    product_codes.append(code)
        return product_codes

    def _get_first_tags_from_response(self, response):
        if "query分类" not in response:
            return []
        if not isinstance(response["query分类"], list):
            # 如果不是 list 则默认是配置
            query_labels = ["其他意图"]
            return query_labels
        query_labels = response["query分类"]
        return query_labels

    @property
    def item_name_list(self):
        return self._item_name_list
