import asyncio
import datetime
import json
import time
import traceback
import uuid
from asyncio import TaskGroup

from fastapi import Request
from prometheus_client import Counter, Histogram
from config.model_config import MODEL_VERSION
from config.common_config import get_env_by_key
from config.run_config import RUN_CONFIG_DICT, DATA_TABLE_NAME
from config.chat_config import REFUSAL_MESSAGE_DICT, CANDIDATE_ITEM_SIZE, PRE_MESSAGE_DICT
from core.schema.chat_request import ChatRequest
from core.schema.chat_response import EventType, ChatResponse, ChatResponseData
from core.schema.chat_base import MessageType, IntentType
from core.schema.constant import CHAT
from core.schema.knowledge_source import KnowledgeSource
from core.schema.db_record import DBRecord
from service.base_service import BaseService
from service.model_manager import ModelManager
from service.prompt_build_service import PromptBuildService
from service.query_parse_service import QueryParseService
from util.common_util import decode_sse, encode_sse, get_cur_millis, is_empty
from util.mysql_db_manager import DBManager
from util.string_util import normalize_item_name

import random

ENV_NAME = get_env_by_key("ENV_NAME", "local")


class ChatService(BaseService):
    def __init__(
            self,
            logger,
            counter: Counter,
            timer: Histogram,
            chat_request_id,
            api_key_text,
            request_receive_time,
            query_parse_service: QueryParseService,
            prompt_build_service: PromptBuildService,
            model_manager: ModelManager,
            normalized_item_name_list,
            item_name_xiaomi_list,
            db: DBManager,
            item_name_id_cls_item,
            chat_request: ChatRequest,
    ):
        super().__init__(logger, counter, timer, request_receive_time)
        self._api_key_text = api_key_text
        self._chat_request_id = chat_request_id
        self._query_parse_service: QueryParseService = query_parse_service
        self._prompt_build_service: PromptBuildService = prompt_build_service
        self._model_manager: ModelManager = model_manager
        self._normalized_item_name_list = normalized_item_name_list
        self._item_name_xiaomi_list = item_name_xiaomi_list
        self._db = db
        self._item_name_id_cls_item = item_name_id_cls_item
        self._data_table_name = RUN_CONFIG_DICT.get(ENV_NAME).get(DATA_TABLE_NAME)
        self._actual_item_name_list = []
        self._first_tags = []
        self._second_tags = []
        self._second_tags_map_dict = None
        self._task_filter = None
        self._task_answer_first_event = None
        self._task_retrieval_minet = None
        self._task_retrieval_doc = None
        self._task_retrieval_faq = None
        self._user_prompt = ""
        self._candidate_item_size = CANDIDATE_ITEM_SIZE
        self._chat_request = chat_request
        self._selected_item = chat_request.chat_history[-1].messages[0].selected_item

    async def chat(self, chat_request: ChatRequest, http_request: Request):
        chat_request_json = json.dumps(chat_request.to_dict(), indent=2, ensure_ascii=False)
        self._logger.info(f"收到请求：\n{chat_request_json}")
        answer_start_time = None
        first_token_elapse = 0
        response_id = str(uuid.uuid4())
        async for chat_response in self.chat_inner(chat_request, http_request):
            if chat_response.event == EventType.START_EVENT:
                answer_start_time = get_cur_millis()
                first_token_elapse = answer_start_time - self._request_receive_time
                self._logger.debug(f"开始回答耗时：{first_token_elapse / 1000:.2f} 秒，response_id={response_id}")
                self._timer.labels(object="first_token_elapse", condition=chat_response.data.answer_type.name).observe(
                    first_token_elapse)
            chat_response.data.answer_start_time = answer_start_time
            chat_response.conversation_id = chat_request.conversation_id
            # 生成一个 request id 给后端，统一存储，并且和 request id 区分开
            chat_response.request_id = response_id
            chat_response.data.request_receive_time = self._request_receive_time
            chat_response.data.model_version = MODEL_VERSION
            if chat_response.event == EventType.FINISH_EVENT:
                chat_response.data.answer_finish_time = get_cur_millis()
                chat_response_json = json.dumps(chat_response.to_dict(), indent=2, ensure_ascii=False)
                self._logger.debug(f"回答完成，回答内容：\n{chat_response_json}")
                if chat_request.version == 1 and chat_request.debug:
                    chat_response.data.time_cost = self.get_execution_time_summary()
                chat_response.data.total_tokens = self.get_total_token_usage()
                self.insert_to_db(chat_request, chat_response, first_token_elapse)
                if chat_request is None or not chat_request.debug:
                    # 前端要求最后一个 FINISH_EVENT text 为空串（为了和中国区逻辑兼容）
                    chat_response.data.text = ""
            # to sse str, starts with data:
            yield encode_sse(chat_response)

    async def chat_inner(self, chat_request: ChatRequest, http_request: Request):
        self._logger.debug(f"item name: {chat_request.item_name}")
        if chat_request.version is None:
            # 处理 430 单轮对话
            self._logger.debug(f"执行430版单轮问答")
            # 统计 430 版本请求次数
            self._counter.labels(object=CHAT, condition="mvp").inc()
            async for response in self.single_round_chat_430(chat_request, http_request):
                yield response
            return

        elif chat_request.version == 1:
            # 处理 530 版本
            # 统计 530 版本请求次数
            self._counter.labels(object=CHAT, condition="530").inc()
            start_time = time.time()
            async for response in self.current_round_chat(chat_request, http_request):
                if response.event == EventType.FINISH_EVENT:
                    self.record_execution_time('530_response_time', start_time)
                yield response
            return

        self._logger.error(f"暂不支持的 chat version {chat_request.version}")
        raise RuntimeError(f"暂不支持的 chat version {chat_request.version}")

    def insert_to_db(self, chat_request, finish_chat_response, first_token_elapse):
        try:
            start_time = time.time()
            insert_record = DBRecord(
                conversation_id=chat_request.conversation_id,
                question_id=self._chat_request_id,
                add_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                question_content=chat_request.ending_message,
                response=finish_chat_response.data.text,
                first_token_elapse=first_token_elapse,
                request_receive_time=finish_chat_response.data.request_receive_time,
                answer_start_time=finish_chat_response.data.answer_start_time,
                answer_finish_time=finish_chat_response.data.answer_finish_time,
                total_tokens=finish_chat_response.data.total_tokens,
                answer_type=finish_chat_response.data.answer_type,
                intents=",".join(self._first_tags),
                selected_item_names=chat_request.item_name,
                actual_item_names=",".join(self._actual_item_name_list),
                item_attributes=",".join(self._second_tags),
                model_version=MODEL_VERSION,
                dt=datetime.datetime.now().strftime('%Y%m%d'),
                prompt=self._user_prompt,
                system_language=chat_request.language.value,
                # 暂时使用后端传来的语言类型，后续考虑增加语言类型的识别
                input_language=chat_request.language.value,
                output_language=chat_request.language.value,
                area=chat_request.area.value,
                version=chat_request.version,
                response_id=finish_chat_response.request_id,
            ).to_dict()
            self._logger.debug(f"写入数据库：{insert_record}")
            self._db.insert_record(self._data_table_name, insert_record)
            self._logger.debug(f"insert_data: {time.time() - start_time}")
        except Exception as e:
            self._logger.error(f"写入数据库发生错误 {str(e)}: {traceback.format_exc()}")

    async def single_round_chat_430(self, chat_request: ChatRequest, http_request: Request):
        self._logger.debug(f"收到问题：{chat_request.ending_message}")
        # 如果数据中暂时不支持当前机型，直接拒答
        if chat_request.item_name_normalized not in self._normalized_item_name_list:
            self._logger.error(f"接收到不支持的机型：{chat_request.item_name}({chat_request.item_name_normalized})")
            self._logger.debug(f"当前支持的机型：{self._normalized_item_name_list}")
            # 返回拒答固定话术
            for response in self.refuse_answer(MessageType.NOT_SUPPORTED_ITEM):
                yield response
            return

        async with TaskGroup() as tg:
            # init task
            self._task_filter = tg.create_task(
                self._query_parse_service.filter_by_first_tags_430(chat_request, self._chat_request_id, tg),
                name="过滤任务",
            )
            # 只是一个异步生成器对象，而不会立即执行函数体里的代码
            answer_agent = self.generate_response_stream_430(chat_request, http_request, tg)
            # _task_answer 只要 yield 第一个 event 就算完成了
            self._task_answer_first_event = tg.create_task(
                answer_agent.__anext__(), name="回答任务"
            )
            done_tasks, pending_tasks = await asyncio.wait(
                {self._task_filter, self._task_answer_first_event},
                return_when=asyncio.FIRST_COMPLETED,
            )
            if self._task_filter in done_tasks:
                # 如果过滤任务先返回结果了：根据过滤结果决定是否继续回答
                answer_type, self._actual_item_name_list, self._first_tags, total_tokens_filter_by_first_tags = self._task_filter.result()
                self._logger.info(f"过滤结果先返回了：{answer_type}, {self._first_tags}")
                if answer_type == MessageType.TEXT:
                    # 通过了，则等待 _task_answer 任务完成
                    self._counter.labels(object=CHAT, condition=MessageType.TEXT.name).inc()
                    first_response = await self._task_answer_first_event
                    yield first_response
                    # 此后，持续迭代剩余的事件
                    async for response in answer_agent:
                        if chat_request.debug and response.event == EventType.FINISH_EVENT:
                            response.data.total_tokens += total_tokens_filter_by_first_tags
                        yield response
                    return

                # 拒答，取消其他任务
                await self.cancel_tasks(
                    [
                        self._task_answer_first_event,
                        self._task_retrieval_doc,
                        self._task_retrieval_faq,
                        self._task_retrieval_minet,
                    ]
                )
                # 返回拒答固定话术
                for response in self.refuse_answer(answer_type):
                    if chat_request.debug and response.event == EventType.FINISH_EVENT:
                        response.data.total_tokens += total_tokens_filter_by_first_tags
                    yield response
                return

            if self._task_answer_first_event in done_tasks:
                # 如果回答任务先返回第一个 event
                self._logger.info("回答先返回了第一个 event，继续等待过滤任务完成")
                await self._task_filter
                answer_type, self._actual_item_name_list, self._first_tags, total_tokens_filter_by_first_tags = self._task_filter.result()
                self._logger.info(f"过滤结果在回答之后返回了：{answer_type}, {self._first_tags}")
                if answer_type == MessageType.TEXT:
                    self._counter.labels(object=CHAT, condition=MessageType.TEXT.name).inc()
                    # 开始回答
                    first_response = self._task_answer_first_event.result()
                    yield first_response
                    # 此后，持续迭代剩余的事件
                    async for response in answer_agent:
                        if chat_request.debug and response.event == EventType.FINISH_EVENT:
                            response.data.total_tokens += total_tokens_filter_by_first_tags
                        yield response
                    return

                # 拒答，取消其他任务
                await self.cancel_tasks(
                    [
                        self._task_answer_first_event,
                        self._task_retrieval_doc,
                        self._task_retrieval_faq,
                        self._task_retrieval_minet,
                    ]
                )
                # 返回拒答固定话术
                for response in self.refuse_answer(answer_type):
                    if chat_request.debug and response.event == EventType.FINISH_EVENT:
                        response.data.total_tokens += total_tokens_filter_by_first_tags
                    yield response
                return

    # 单轮问答意图回答-530
    async def single_round_chat(self, chat_request: ChatRequest, http_request: Request, item_name: str):
        self._logger.debug(f"收到问题：{chat_request.ending_message}")
        # 如果数据中暂时不支持当前机型，直接拒答
        item_name_normalized = normalize_item_name(item_name)
        if item_name_normalized not in self._normalized_item_name_list:
            self._logger.error(f"接收到不支持的机型：{item_name}({item_name_normalized})")
            self._logger.debug(f"当前支持的机型：{self._normalized_item_name_list}")
            # 返回拒答固定话术
            for response in self.refuse_answer(MessageType.NOT_SUPPORTED_ITEM):
                yield response
            return

        async with TaskGroup() as tg:
            # init task
            # 只是一个异步生成器对象，而不会立即执行函数体里的代码
            chat_request.item_name = item_name
            chat_request.item_name_normalized = item_name_normalized
            updated_selected_item, updated_item_list = self.get_updated_item_list_and_selected_item([item_name])
            answer_agent = self.generate_response_stream(chat_request, http_request, tg)
            # _task_answer 只要 yield 第一个 event 就算完成了
            self._task_answer_first_event = tg.create_task(answer_agent.__anext__(), name="回答任务")
            # 如果回答任务先返回第一个 event
            self._counter.labels(object=CHAT, condition=MessageType.TEXT.name).inc()
            # 开始回答
            first_response = await self._task_answer_first_event
            # 更新吸顶机型
            first_response.data.selected_item = updated_selected_item
            yield first_response
            # 此后，持续迭代剩余的事件
            async for response in answer_agent:
                # 更新吸顶机型
                response.data.selected_item = updated_selected_item
                yield response
            return

    # 用户确认机型回答-530
    def user_selected_answer(self, content, updated_item_list, answer_type):
        chat_response = ChatResponse(
            event=EventType.START_EVENT,
            data=ChatResponseData(answer_type=answer_type,
                                  item_list=updated_item_list)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.TEXT_CHUNK_EVENT,
            data=ChatResponseData(text=content,
                                  answer_type=answer_type,
                                  item_list=updated_item_list)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.FINISH_EVENT,
            data=ChatResponseData(text=content,
                                  answer_type=answer_type,
                                  item_list=updated_item_list)
        )
        yield chat_response

    # 问答类意图响应-530
    async def question_answer_response_stream(self, chat_request: ChatRequest, http_request: Request, exact_item_names,
                                              fuzzy_item_names):
        is_spu_fuzzy = False
        if len(set(fuzzy_item_names) - set(exact_item_names)) > 0:
            is_spu_fuzzy = True
        fuzzy_item_names_with_exact = list(set(exact_item_names + fuzzy_item_names))
        self._logger.debug(f'fuzzy_item_names_with_exact: {fuzzy_item_names_with_exact}')

        # 分类机型名称为xiaomi和non_xiaomi
        xiaomi_exact, non_xiaomi_exact = self.categorize_item_names(exact_item_names)
        xiaomi_fuzzy_with_exact, non_xiaomi_fuzzy_with_exact = self.categorize_item_names(fuzzy_item_names_with_exact)

        # 判断有无吸顶机型
        has_pinned_item = self._selected_item is not None
        self._logger.debug(f"{'有' if has_pinned_item else '没有'}吸顶机型")

        if not has_pinned_item:
            if len(fuzzy_item_names_with_exact) <= 0:
                self._logger.debug(f"未识别出query中的机型，返回拒答")
                # 返回拒答固定话术
                for response in self.refuse_answer(MessageType.ITEM_UNRECOGNIZED):
                    yield response
                return

            if len(xiaomi_exact) <= 0:
                # 提供给用户候选机型
                candidates = self.provide_candidate_items(xiaomi_fuzzy_with_exact)
                content = REFUSAL_MESSAGE_DICT[MessageType.ITEM_CANDIDATE]
                for response in self.user_selected_answer(content, candidates,
                                                          answer_type=MessageType.ITEM_CANDIDATE):
                    yield response
                return

            # 直接更新吸顶机型
            self._logger.debug("精确识别到xiaomi机型，更新吸顶机型。")
            # 选择一个xiaomi机型进行更新
            new_pinned_item = xiaomi_exact[0]
            self._logger.debug(f"更新吸顶机型为: {new_pinned_item}")
            self._logger.debug(f"识别出query中的确定性机型 || {new_pinned_item}，进行问答流程")
            async for response in self.single_round_chat(chat_request, http_request, new_pinned_item):
                yield response
            return

        if len(fuzzy_item_names_with_exact) <= 0:
            # 将原始吸顶机型作为更新后的机型
            self._logger.debug("未识别出任何机型，使用原始的吸顶机型作为回答。")
            # 使用原始吸顶机型作为回答
            async for response in self.single_round_chat(chat_request, http_request, self._selected_item.item_name):
                yield response
            return

        # 根据is_spu_fuzzy的值，判断是否模糊，如果是True（模糊），则从模糊列表中拿出5个机型作为候选，提供给用户，
        # 注意：需要优先从xiaomi列表中选，不足的，再从非xiaomi机型中选择；如果不模糊，则判断exact_item_names 精确识别机型的列表
        # 是否为空，如果是空，则把原始的吸顶机型作为更新后的机型，否则，判断吸顶机型是否在识别出的精确机型中，如果在，则保持原始的吸顶机型，
        # 否则，需要从精确识别的xiaomi机型中，拿出第一个机型作为更新后的吸顶机型
        if is_spu_fuzzy:
            self._logger.debug("识别结果为模糊识别，提供候选机型。")
            # 提供候选机型（最多5个），优先从xiaomi列表中选
            candidates = self.provide_candidate_items(xiaomi_fuzzy_with_exact)
            content = REFUSAL_MESSAGE_DICT[MessageType.ITEM_CANDIDATE]
            for response in self.user_selected_answer(content, candidates,
                                                      answer_type=MessageType.ITEM_CANDIDATE):
                yield response
            return

        self._logger.debug("识别结果为精确识别，处理精确机型。")
        if len(xiaomi_exact) <= 0:
            # 没有精确识别到xiaomi机型，保持原始吸顶机型
            async for response in self.single_round_chat(chat_request, http_request, self._selected_item.item_name):
                yield response
            return

        if self._selected_item.item_name in xiaomi_exact:
            self._logger.debug("吸顶机型在精确识别的机型列表中，保持原始吸顶机型。")
            # 保持原始吸顶机型
            async for response in self.single_round_chat(chat_request, http_request,
                                                         self._selected_item.item_name):
                yield response
            return

        # 更新吸顶机型为精确识别到的第一个xiaomi机型
        new_pinned_item = xiaomi_exact[0]
        self._logger.debug(f"更新吸顶机型为: {new_pinned_item}")
        self._logger.debug(f"识别出query中的确定性机型 || {new_pinned_item}，进行问答流程")
        async for response in self.single_round_chat(chat_request, http_request, new_pinned_item):
            yield response

    def provide_candidate_items(self, xiaomi_candidates):
        """
        提供给用户候选机型。

        参数:
            xiaomi_candidates (list): 候选的xiaomi机型。

        生成:
            candidates: 生成的候选机型列表。
        """
        candidates = []
        # 优先从xiaomi_candidates中选取
        for name in xiaomi_candidates:
            if len(candidates) < self._candidate_item_size:
                candidates.append(name)
            else:
                break
        if len(candidates) < self._candidate_item_size:
            other_candidates = random.sample(list(set(self._item_name_xiaomi_list) - set(candidates)),
                                             self._candidate_item_size - len(candidates))
            candidates.extend(other_candidates)
        candidates = list(set(candidates))
        candidates = [self.get_item_by_name(name) for name in candidates]
        assert len(candidates) == self._candidate_item_size, f"候选机型数量不足 {self._candidate_item_size} 个"
        return candidates

    def get_updated_item_list_and_selected_item(self, recognize_query_item_name_list):
        updated_item_list = [
            self._item_name_id_cls_item.get(x, "") for x in recognize_query_item_name_list
        ]
        updated_selected_item = updated_item_list[0]
        return updated_selected_item, updated_item_list

    # 双机对比意图回答-530
    def item_compare_answer(self, content, updated_selected_item, updated_item_list, answer_type):
        chat_response = ChatResponse(
            event=EventType.START_EVENT,
            data=ChatResponseData(answer_type=answer_type,
                                  selected_item=updated_selected_item,
                                  item_list=updated_item_list)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.TEXT_CHUNK_EVENT,
            data=ChatResponseData(text=content,
                                  answer_type=answer_type,
                                  selected_item=updated_selected_item,
                                  item_list=updated_item_list)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.FINISH_EVENT,
            data=ChatResponseData(text=content,
                                  answer_type=answer_type,
                                  selected_item=updated_selected_item,
                                  item_list=updated_item_list)
        )
        yield chat_response

    # 双机对比意图响应
    def item_compare_response_stream(self, recognize_query_item_names, extracted_item_names):
        # 分类机型名称为xiaomi和non_xiaomi
        item_names_xiaomi, item_names_non_xiaomi = self.categorize_item_names(recognize_query_item_names)
        not_in_candidates = self.get_item_name_not_in_candidates(extracted_item_names)
        self._logger.debug(f"item_names_xiaomi: {item_names_xiaomi}")
        self._logger.info(f"item_names_non_xiaomi: {item_names_non_xiaomi}")
        # 判断有无吸顶机型
        has_pinned_item = self._selected_item is not None
        self._logger.debug(f"{'有' if has_pinned_item else '没有'}吸顶机型")

        if not has_pinned_item:
            self._logger.debug("无吸顶机型，识别query中的机型。")
            # 识别query中的机型
            if len(item_names_xiaomi) + len(item_names_non_xiaomi) < 2:
                self._logger.debug("识别到的机型少于2个，无法进行双机对比，拒答。")
                for response in self.refuse_answer(MessageType.ITEM_COMPARE_UNCERTAIN):
                    if len(not_in_candidates) > 0 and (
                            response.event == EventType.FINISH_EVENT or response.event == EventType.TEXT_CHUNK_EVENT):
                        response.data.text = REFUSAL_MESSAGE_DICT[MessageType.ITEM_COMPARE]
                    yield response
                return

            # 确保至少有一个xiaomi机型
            if not item_names_xiaomi:
                self._logger.debug("没有xiaomi机型可用于对比，拒答。")
                for response in self.refuse_answer(MessageType.NO_XIAOMI_ITEM):
                    yield response
                return

            # 选择至少一个xiaomi机型
            updated_selected_item = item_names_xiaomi.pop(0)
            if item_names_xiaomi:
                second_item = item_names_xiaomi.pop(0)
            elif item_names_non_xiaomi:
                second_item = item_names_non_xiaomi.pop(0)
            else:
                self._logger.debug("只有一个xiaomi机型且没有非xiaomi机型可用于对比，拒答。")
                for response in self.refuse_answer(MessageType.ITEM_COMPARE_UNCERTAIN):
                    if len(not_in_candidates) > 0 and (
                            response.event == EventType.FINISH_EVENT or response.event == EventType.TEXT_CHUNK_EVENT):
                        response.data.text = REFUSAL_MESSAGE_DICT[MessageType.ITEM_COMPARE]
                    yield response
                return

            updated_item_list = [updated_selected_item, second_item]

        else:
            self._logger.debug(f"当前吸顶机型: {self._selected_item}")
            total_recognized = len(item_names_xiaomi) + len(item_names_non_xiaomi)

            if total_recognized < 1:
                self._logger.debug("没有足够的机型进行双机对比，拒答。")
                for response in self.refuse_answer(MessageType.ITEM_COMPARE_UNCERTAIN):
                    if len(not_in_candidates) > 0 and (
                            response.event == EventType.FINISH_EVENT or response.event == EventType.TEXT_CHUNK_EVENT):
                        response.data.text = REFUSAL_MESSAGE_DICT[MessageType.ITEM_COMPARE]
                    yield response
                return

            if total_recognized == 1:
                # 只有一个识别到的机型，使用原始吸顶机型与该机型对比
                second_item = item_names_xiaomi.pop(0) if item_names_xiaomi else item_names_non_xiaomi.pop(0)
                updated_selected_item = self._selected_item.item_name  # 使用原始吸顶机型
                updated_item_list = [updated_selected_item, second_item]
            else:
                # 识别到的机型数量大于1
                if item_names_xiaomi:
                    # 更新吸顶机型为识别到的第一个xiaomi机型
                    new_pinned_item = item_names_xiaomi.pop(0)
                    self._logger.debug(f"更新吸顶机型为: {new_pinned_item}")
                    updated_selected_item = new_pinned_item
                    # 选择下一个机型进行对比
                    if item_names_xiaomi:
                        second_item = item_names_xiaomi.pop(0)
                    elif item_names_non_xiaomi:
                        second_item = item_names_non_xiaomi.pop(0)
                    else:
                        self._logger.debug("没有足够的机型进行双机对比，拒答。")
                        for response in self.refuse_answer(MessageType.ITEM_COMPARE_UNCERTAIN):
                            if len(not_in_candidates) > 0 and (
                                    response.event == EventType.FINISH_EVENT or response.event == EventType.TEXT_CHUNK_EVENT):
                                response.data.text = REFUSAL_MESSAGE_DICT[MessageType.ITEM_COMPARE]
                            yield response
                        return
                    updated_item_list = [updated_selected_item, second_item]
                else:
                    # 没有xiaomi机型，使用原始吸顶机型与一个非xiaomi机型对比
                    second_item = item_names_non_xiaomi.pop(0)
                    updated_selected_item = self._selected_item.item_name  # 使用原始吸顶机型
                    updated_item_list = [updated_selected_item, second_item]

        self._logger.debug(f"updated_selected_item: {updated_selected_item}")
        self._logger.debug(f"updated_item_list: {updated_item_list}")
        updated_selected_item = self.get_item_by_name(updated_selected_item)
        updated_item_list = [self.get_item_by_name(item_name) for item_name in updated_item_list]
        # 构造对比内容
        content = ' || '.join([item.item_name for item in updated_item_list])
        self._logger.debug(f"进行双机对比，机型列表: {content}")
        content = REFUSAL_MESSAGE_DICT[MessageType.ITEM_COMPARE]
        # 返回双机对比响应
        for response in self.item_compare_answer(content, updated_selected_item, updated_item_list,
                                                 MessageType.ITEM_COMPARE):
            yield response

    def get_item_name_not_in_candidates(self, extracted_item_names):
        normalized_candidates = set(self._normalized_item_name_list)

        # 保留未在候选列表中的原始名称
        not_in_candidates = [
            name for name in extracted_item_names
            if normalize_item_name(name) not in normalized_candidates
        ]
        return not_in_candidates

    def categorize_item_names(self, recognize_query_item_name_list):
        """
        将识别到的机型名称分类为xiaomi和non_xiaomi。

        参数:
            recognize_query_item_name_list (list): 识别到的机型名称列表（已规范化）。

        返回:
            tuple: 包含两个列表，第一个是xiaomi机型，第二个是non_xiaomi机型。
        """
        item_names_xiaomi = []
        item_names_non_xiaomi = []

        for item_name in recognize_query_item_name_list:
            if item_name in self._item_name_xiaomi_list:
                item_names_xiaomi.append(item_name)
            else:
                item_names_non_xiaomi.append(item_name)

        return item_names_xiaomi, item_names_non_xiaomi

    def get_item_by_name(self, item_name):
        return self._item_name_id_cls_item.get(item_name, "")

    # 自由问答意图响应
    async def free_question_response_stream(self, chat_request: ChatRequest, http_request: Request, answer_intent=None):
        # TODO: 构造自由问答的 prompt
        if answer_intent:
            user_prompt = self._prompt_build_service.build_free_question_reject_prompt(chat_request, answer_intent)
            answer_type = MessageType.FREE_FAQ_REJECT
        else:
            user_prompt = self._prompt_build_service.build_free_question_prompt(chat_request)
            answer_type = MessageType.FREE_FAQ_ANSWER

        start_time = time.time()
        is_first_chunk = True
        async for response in self._model_manager.call_llm_with_stream(user_prompt, self._api_key_text):
            if await http_request.is_disconnected():
                raise RuntimeError("客户端已断开连接")

            llm_response_data_dict = decode_sse(response.decode("utf-8"))
            if is_empty(llm_response_data_dict) or "event" not in llm_response_data_dict:
                continue

            cur_event_type = llm_response_data_dict["event"]
            if cur_event_type == "text_chunk":
                if is_first_chunk:
                    chat_response = ChatResponse(
                        event=EventType.START_EVENT,
                        data=ChatResponseData(answer_type=answer_type)
                    )
                    is_first_chunk = False
                    yield chat_response

                chat_response = ChatResponse(
                    event=EventType.TEXT_CHUNK_EVENT,
                    data=ChatResponseData(
                        text=llm_response_data_dict["data"]["text"],
                        answer_type=answer_type
                    )
                )
                yield chat_response

            if cur_event_type == "workflow_finished":
                response_text = llm_response_data_dict["data"]["outputs"]["answer"]
                response_data = ChatResponseData(text=response_text, answer_type=answer_type)
                if chat_request.debug:
                    response_data.prompt = user_prompt
                    response_data.total_tokens = llm_response_data_dict["data"][
                        "total_tokens"]
                chat_response = ChatResponse(event=EventType.FINISH_EVENT, data=response_data)
                self.record_execution_time('call_llm_with_stream', start_time)
                self.record_token_usage('call_llm_with_stream', llm_response_data_dict["data"]["total_tokens"])
                yield chat_response

    async def current_round_chat(self, chat_request: ChatRequest, http_request: Request):
        # 判断用户对话类型, 当前对话类型，目前有2种类型：1-文本 7-用户确认后的机型
        if chat_request.chat_history[-1].messages[0].type == MessageType.ITEM_CONFIRM:
            # 用户确认的机型
            if self._selected_item:
                user_item_name = self._selected_item.item_name
            else:
                user_item_name = chat_request.chat_history[-1].messages[0].item_list[0].item_name
            # 更新用户的问题
            if chat_request.chat_history[-1].messages[0].content is None:
                chat_request.ending_message = chat_request.chat_history[-3].messages[0].content
            else:
                chat_request.ending_message = chat_request.chat_history[-1].messages[0].content
            self._logger.debug(f"用户确认后的机型为{user_item_name}，进行问答流程")
            async for response in self.single_round_chat(chat_request, http_request, user_item_name):
                yield response
            return

        self._logger.debug(f"根据用户提问判断意图")
        # 判断意图, 意图分为问答类、双机对比、自由问答
        item_name = ""
        if self._selected_item is not None:
            item_name = self._selected_item.item_name
        start_time = time.time()
        async with TaskGroup() as tg:
            # 创建并启动所有任务
            tag_question_first_task = tg.create_task(self._query_parse_service.tag_question_first(
                chat_request.ending_message, item_name, http_request
            ))
            extract_item_name_task = tg.create_task(self._query_parse_service.extract_item_names(
                chat_request.ending_message, http_request
            ))
            recognize_exact_task = tg.create_task(
                self._query_parse_service.recognize_item_names_for_chunk(chat_request, chat_request.request_id, tg,
                                                                         'exact'))
            recognize_fuzzy_task = tg.create_task(
                self._query_parse_service.recognize_item_names_for_chunk(chat_request, chat_request.request_id, tg,
                                                                         "fuzzy"))
            second_tags_task = tg.create_task(self._query_parse_service.recognize_tags(
                chat_request.ending_message, http_request
            ))

            answer_intent, tag_question_first_task_token, tag_question_first_task_start_time = await tag_question_first_task
            self.record_token_usage('tag_question_first', tag_question_first_task_token)
            extracted_item_names, extract_item_names_task_token, extract_item_names_task_start_time = await extract_item_name_task
            self.record_token_usage('extracted_item_names', extract_item_names_task_token)
            second_tags, second_tags_task_token, second_tags_task_start_time = await second_tags_task
            self._second_tags = list(second_tags.keys())
            self._second_tags_map_dict = second_tags
            self._logger.debug(f"识别出的二级标签：{','.join(self._second_tags)}")
            self.record_token_usage('second_tags', second_tags_task_token)

            # 检查意图是否为自由问答类
            if answer_intent in IntentType.FREE_QUESTION_REJECT.as_set() or answer_intent in IntentType.FREE_QUESTION.as_set():  # 根据实际的自由问答意图类型调整
                # 取消机型识别
                await self.cancel_tasks([recognize_exact_task, recognize_fuzzy_task])
            else:
                # 等待机型识别任务完成
                exact_item_names, recognize_exact_task_token, recognize_exact_task_start_time = await recognize_exact_task
                self.record_token_usage('recognize_exact_task', recognize_exact_task_token)
                self._logger.debug(f'exact_item_names: {exact_item_names}')
                fuzzy_item_names, recognize_fuzzy_task_token, recognize_fuzzy_task_start_time = await recognize_fuzzy_task
                self.record_token_usage('recognize_fuzzy_task', recognize_fuzzy_task_token)
                self._logger.debug(f'fuzzy_item_names: {fuzzy_item_names}')
        self.record_execution_time('level_1_async', start_time)
        self._logger.debug(f"意图识别: {answer_intent}")
        if answer_intent in IntentType.FREE_QUESTION_REJECT.as_set():
            # 自由问答
            self._logger.debug(f"提问为: 3c数码产品无关的自由问答类意图")
            async for response in self.free_question_response_stream(chat_request, http_request, answer_intent):
                response.data.selected_item = self._selected_item
                yield response
            return
        elif answer_intent in IntentType.FREE_QUESTION.as_set():
            # 自由问答
            self._logger.debug(f"提问为: 3c数码产品相关的自由问答类意图")
            async for response in self.free_question_response_stream(chat_request, http_request):
                response.data.selected_item = self._selected_item
                yield response
            return
        elif answer_intent in IntentType.ITEM_COMPARE.as_set():
            # 双机对比
            self._logger.debug(f"提问为: 双机对比类意图")
            for response in self.item_compare_response_stream(exact_item_names, extracted_item_names):
                yield response
            return
        else:
            # 问答类
            # 当前对话类型，目前有2种类型：1-文本 7-用户确认的机型
            self._logger.debug(f"提问为: 问答类意图")
            async for response in self.question_answer_response_stream(chat_request, http_request, exact_item_names,
                                                                       fuzzy_item_names):
                yield response
            return

    def refuse_answer(self, refuse_type: MessageType):
        # 更新统计
        self._counter.labels(object=CHAT, condition=refuse_type.name).inc()
        # 生成并返回拒绝回答的事件
        refusal_message = REFUSAL_MESSAGE_DICT[refuse_type]
        for chat_response in self.wrap_str_to_response_stream(refusal_message, answer_type=refuse_type):
            chat_response.data.selected_item = self._selected_item
            yield chat_response

    async def cancel_tasks(self, task_list):
        for task in task_list:
            if task is None:
                continue

            if task.done():
                self._logger.debug(f"{task.get_name()} 无需取消或已结束")
                continue

            task.cancel()
            try:
                await task
            except (asyncio.CancelledError, StopAsyncIteration):
                self._logger.debug(f"{task.get_name()} 已取消")
            except Exception as e:
                self._logger.error(f"{task.get_name()} 取消时出错: {e}")

    async def execute_knowledge_retrieval(self, ending_msg, second_tag_map_dict, item_name_normalized,
                                          task_group: TaskGroup):
        self._task_retrieval_doc = task_group.create_task(
            self._prompt_build_service.retrieve_doc_knowledge(ending_msg, second_tag_map_dict, item_name_normalized),
            name="知识检索任务_DOC",
        )
        return {
            KnowledgeSource.DOC: self._task_retrieval_doc,
        }

    async def get_retrieval_results(self, tasks):
        # 等待所有任务完成并获取结果
        knowledge_dict = {}
        for source, task in tasks.items():
            try:
                knowledge_dict[source] = await task
            except Exception as e:
                self._logger.error(
                    f"{task.get_name()}失败，prompt 构建时信息可能缺失 {str(e)}: {traceback.format_exc()}")
        return knowledge_dict

    async def generate_response_stream_430(self, chat_request, http_request, task_group: TaskGroup):
        self.log_elapse("二级标签")
        start_time = time.time()
        second_tag_map_dict, total_tokens_recognize_tage, _ = await self._query_parse_service.recognize_tags(
            chat_request.ending_message, self._chat_request_id
        )
        self.record_execution_time('recognize_tags', start_time)
        self.record_token_usage('recognize_tags', total_tokens_recognize_tage)
        self._second_tags = list(second_tag_map_dict.keys())
        self._logger.debug(f"识别出的二级标签：{','.join(self._second_tags)}")
        start_time = time.time()
        retrieval_task_list = await self.execute_knowledge_retrieval(
            chat_request.ending_message, second_tag_map_dict, chat_request.item_name_normalized, task_group
        )
        self.record_execution_time('execute_knowledge_retrieval', start_time)
        start_time = time.time()
        knowledge_dict = await self.get_retrieval_results(retrieval_task_list)
        self.record_execution_time('get_retrieval_results', start_time)
        self.log_elapse("检索完知识，构建 prompt")
        user_prompt = self._prompt_build_service.build_prompt(chat_request, knowledge_dict)
        if is_empty(user_prompt):
            for response in self.refuse_answer(MessageType.NO_KNOWLEDGE):
                if chat_request.debug and response.event == EventType.FINISH_EVENT:
                    response.data.total_tokens = total_tokens_recognize_tage
                yield response
            return

        start_time = time.time()
        is_first_chunk = True
        async for response in self._model_manager.call_llm_with_stream(user_prompt, self._api_key_text):
            if await http_request.is_disconnected():
                raise RuntimeError("客户端已断开连接")

            llm_response_data_dict = decode_sse(response.decode("utf-8"))
            if is_empty(llm_response_data_dict) or "event" not in llm_response_data_dict:
                continue

            cur_event_type = llm_response_data_dict["event"]
            if cur_event_type == "text_chunk":
                if is_first_chunk:
                    chat_response = ChatResponse(
                        event=EventType.START_EVENT,
                        data=ChatResponseData(answer_type=MessageType.TEXT)
                    )
                    is_first_chunk = False
                    yield chat_response

                chat_response = ChatResponse(
                    event=EventType.TEXT_CHUNK_EVENT,
                    data=ChatResponseData(
                        text=llm_response_data_dict["data"]["text"],
                        answer_type=MessageType.TEXT
                    )
                )
                yield chat_response

            if cur_event_type == "workflow_finished":
                response_text = llm_response_data_dict["data"]["outputs"]["answer"]
                response_data = ChatResponseData(text=response_text, answer_type=MessageType.TEXT)
                if chat_request.debug:
                    response_data.prompt = user_prompt
                    response_data.total_tokens = llm_response_data_dict["data"][
                                                     "total_tokens"] + total_tokens_recognize_tage
                chat_response = ChatResponse(event=EventType.FINISH_EVENT, data=response_data)
                self._user_prompt = user_prompt
                self.record_execution_time('call_llm_with_stream', start_time)
                self.record_token_usage('call_llm_with_stream', llm_response_data_dict["data"]["total_tokens"])
                yield chat_response

    def get_pre_thinking_str(self, chat_request):
        # item_name 不能为空
        if is_empty(chat_request.item_name):
            self._logger.error("单机参数问答时没有传入 item_name")
            return random.sample(PRE_MESSAGE_DICT["NO_ITEM_NAME"], 1)[0]

        if is_empty(self._second_tags_map_dict):
            return random.sample(PRE_MESSAGE_DICT["NO_SECOND_TAG"], 1)[0] + chat_request.item_name

        tag_str = ', '.join(self._second_tags_map_dict.values())
        return random.sample(PRE_MESSAGE_DICT["DEFAULT"], 1)[0] + "tentang aspek " + tag_str + " dari " + chat_request.item_name

    async def generate_response_stream(self, chat_request, http_request, task_group: TaskGroup):
        pre_thinking_str = self.get_pre_thinking_str(chat_request)
        # wrapped_pre_thinking_str = f"*{pre_thinking_str}*\n"
        wrapped_pre_thinking_str = f"%% {pre_thinking_str}\n"
        for chat_response in self.wrap_str_to_response_stream(wrapped_pre_thinking_str, answer_type=MessageType.TEXT,
                                                              is_need_finish=False):
            yield chat_response
        start_time = time.time()
        retrieval_task_list = await self.execute_knowledge_retrieval(
            chat_request.ending_message, self._second_tags_map_dict, chat_request.item_name_normalized, task_group
        )
        self.record_execution_time('execute_knowledge_retrieval', start_time)
        start_time = time.time()
        knowledge_dict = await self.get_retrieval_results(retrieval_task_list)
        self.record_execution_time('get_retrieval_results', start_time)
        self.log_elapse("检索完知识，构建 prompt")
        user_prompt = self._prompt_build_service.build_prompt(chat_request, knowledge_dict)
        if is_empty(user_prompt):
            for response in self.refuse_answer(MessageType.NO_KNOWLEDGE):
                yield response
            return

        start_time = time.time()
        async for response in self._model_manager.call_llm_with_stream(user_prompt, self._api_key_text):
            if await http_request.is_disconnected():
                raise RuntimeError("客户端已断开连接")

            llm_response_data_dict = decode_sse(response.decode("utf-8"))
            if is_empty(llm_response_data_dict) or "event" not in llm_response_data_dict:
                continue

            cur_event_type = llm_response_data_dict["event"]
            if cur_event_type == "text_chunk":
                chat_response = ChatResponse(
                    event=EventType.TEXT_CHUNK_EVENT,
                    data=ChatResponseData(
                        text=llm_response_data_dict["data"]["text"],
                        answer_type=MessageType.TEXT
                    )
                )
                yield chat_response

            if cur_event_type == "workflow_finished":
                response_text = wrapped_pre_thinking_str + llm_response_data_dict["data"]["outputs"]["answer"]
                response_data = ChatResponseData(text=response_text, answer_type=MessageType.TEXT)
                if chat_request.debug:
                    response_data.prompt = user_prompt
                    response_data.total_tokens = llm_response_data_dict["data"]["total_tokens"]
                chat_response = ChatResponse(event=EventType.FINISH_EVENT, data=response_data)
                self._user_prompt = user_prompt
                self.record_execution_time('call_llm_with_stream', start_time)
                self.record_token_usage('call_llm_with_stream', llm_response_data_dict["data"]["total_tokens"])
                yield chat_response

    @staticmethod
    def wrap_str_to_response_stream(content: str, answer_type, is_need_finish=True):
        chat_response = ChatResponse(
            event=EventType.START_EVENT,
            data=ChatResponseData(answer_type=answer_type)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.TEXT_CHUNK_EVENT,
            data=ChatResponseData(text=content, answer_type=answer_type)
        )
        yield chat_response

        if is_need_finish:
            chat_response = ChatResponse(
                event=EventType.FINISH_EVENT,
                data=ChatResponseData(text=content, answer_type=answer_type)
            )
            yield chat_response
