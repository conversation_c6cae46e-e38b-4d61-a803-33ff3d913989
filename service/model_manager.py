import asyncio
import json
import time
import requests

import aiohttp

from service.base_service import BaseService

MIFY_WORKFLOW_URL = "https://mify-be.pt.xiaomi.com/api/v1/workflows/run"

RETRIEVAL_NUM = 20
RETRIEVAL_THRESHOLD = 0
TIMEOUT_MIFY_RETRIEVAL = 5
TIMEOUT_JSON_LLM = 5
TIMEOUT_LLM = 5


class ModelManager(BaseService):
    def __init__(self, logger=None, counter=None, timer=None, request_receive_time=0, chat_request_id="default",
                 dataset_key=None):
        super().__init__(logger, counter, timer, request_receive_time)
        self._chat_request_id = chat_request_id
        self._dataset_key = dataset_key

    async def call_llm_with_stream(self, user_prompt, api_key):
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "inputs": {"user_prompt": user_prompt},
            "response_mode": "streaming",
            "user": f"chat_request_id-{self._chat_request_id}",
        }

        # 记录发送请求的时间
        start_time = time.time()
        async with aiohttp.ClientSession() as session:
            async with session.post(MIFY_WORKFLOW_URL, headers=headers, json=body) as response:
                if response.status != 200:
                    error_msg = f"回答时底层大模型请求失败，状态码={response.status}，api_key={api_key}, 响应内容: {await response.text()}"
                    self._logger.error(error_msg)
                    raise aiohttp.ClientResponseError(
                        response.request_info,
                        response.history,
                        status=response.status,
                        message=error_msg,
                    )

                first_response_received = False
                async for line in response.content:
                    if not first_response_received:
                        # 计算从发送请求到收到第一个响应的时间
                        elapsed_time = time.time() - start_time
                        self._logger.debug(f"回答推理耗时: {elapsed_time:.2f} 秒")
                        first_response_received = True
                    yield line

    async def retrieve_knowledge_with_score(self, query, dataset_id, retrieval_num=RETRIEVAL_NUM, ):
        url = f"https://mify-be.pt.xiaomi.com/api/v1/datasets/{dataset_id}/retrieve"
        headers = {
            "Authorization": f"Bearer {self._dataset_key}",
            "Content-Type": "application/json",
        }
        body = {
            "query": query,
            "retrieval_model": {
                "search_method": "semantic_search",
                "reranking_enable": False,
                "reranking_mode": None,
                "reranking_model": {"reranking_provider_name": "", "reranking_model_name": ""},
                "weights": None,
                "top_k": retrieval_num,
                "score_threshold_enabled": False,
                "score_threshold": None,
            },
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(url, headers=headers, json=body,
                                        timeout=aiohttp.ClientTimeout(total=TIMEOUT_MIFY_RETRIEVAL)) as response:
                    response_json = await response.json()
                    result = list()
                    for record in response_json.get("records", []):
                        result.append({
                            "content": record["segment"]["content"],
                            "score": record["score"]
                        })
                    return result
            except asyncio.TimeoutError:
                self._logger.warning(f"retrieve_knowledge_from_mify timed out after {TIMEOUT_MIFY_RETRIEVAL} seconds")
                return list()

    async def retrieve_knowledge_from_mify(self, query, dataset_id, retrieval_num=RETRIEVAL_NUM,
                                           retrieval_threshold=RETRIEVAL_THRESHOLD):
        url = f"https://mify-be.pt.xiaomi.com/api/v1/datasets/{dataset_id}/retrieve"
        headers = {
            "Authorization": f"Bearer {self._dataset_key}",
            "Content-Type": "application/json",
        }
        body = {
            "query": query,
            "retrieval_model": {
                "search_method": "semantic_search",
                "reranking_enable": False,
                "reranking_mode": None,
                "reranking_model": {"reranking_provider_name": "", "reranking_model_name": ""},
                "weights": None,
                "top_k": retrieval_num,
                "score_threshold_enabled": False,
                "score_threshold": None,
            },
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(url, headers=headers, json=body,
                                        timeout=aiohttp.ClientTimeout(total=TIMEOUT_MIFY_RETRIEVAL)) as response:
                    response_json = await response.json()
                    result = list()
                    for record in response_json.get("records", []):
                        if record["score"] >= retrieval_threshold:
                            self._logger.debug(
                                f"Retrieved segment: {record['segment']['content']} with score: {record['score']}"
                            )
                            result.append(record["segment"]["content"])
                    return result
            except asyncio.TimeoutError:
                self._logger.warning(f"retrieve_knowledge_from_mify timed out after {TIMEOUT_MIFY_RETRIEVAL} seconds")
                return list()

    async def call_llm_with_json(self, prompt, api_key, chat_request_id="unknown"):
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "inputs": {"user_prompt": prompt},
            "response_mode": "blocking",
            "user": f"chat_request_id-{chat_request_id}",
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(MIFY_WORKFLOW_URL, headers=headers, data=json.dumps(body),
                                        timeout=aiohttp.ClientTimeout(total=TIMEOUT_JSON_LLM)) as response:
                    if response.status == 200:
                        result = await response.json()
                        try:
                            return True, result["data"]["outputs"]["answer"], result["data"]["total_tokens"]
                        except Exception as e:
                            self._logger.error(
                                f"call_llm_with_json 返回的数据无法解析(data->outputs->answer): {result}")
                            return False, [], 0
                    else:
                        self._logger.error(
                            f"问题理解时底层大模型请求失败（json），状态码={response.status}, api_key={api_key}，响应内容: {await response.text()}")
                        return False, [], 0
            except asyncio.TimeoutError:
                self._logger.warning(f"call_llm_with_json timed out after {TIMEOUT_JSON_LLM} seconds")
                return False, [], 0

    async def translate_any2zn(self, content, api_key_translate, request_id="unknown"):
        headers = {
            "Authorization": f"Bearer {api_key_translate}",
            "Content-Type": "application/json",
        }
        data = {
            "inputs": {
                "content": content,
            },
            "response_mode": "blocking",
            "user": f"translate_request_id-{request_id}",
        }
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(MIFY_WORKFLOW_URL, headers=headers, json=data,
                                        timeout=aiohttp.ClientTimeout(total=TIMEOUT_JSON_LLM)) as response:
                    if response.status == 200:
                        result = await response.json()
                        output_dict = result["data"]["outputs"]
                        return output_dict["answer"]
                    else:
                        self._logger.error(
                            f"translate_any2zn_async 请求失败，状态码={response.status}，响应内容: {await response.text()}")
                        return str(response.status) + " " + await response.text()
            except asyncio.TimeoutError:
                self._logger.warning(f"translate_any2zn_async timed out after {TIMEOUT_JSON_LLM} seconds")
                return None

    async def call_llm_async(self, user_prompt, api_key):
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "inputs": {"user_prompt": user_prompt},
            "response_mode": "blocking",
            "user": f"chat_request_id-{self._chat_request_id}",
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(MIFY_WORKFLOW_URL, headers=headers, data=json.dumps(body),
                                        timeout=aiohttp.ClientTimeout(total=TIMEOUT_LLM)) as response:
                    if response.status == 200:
                        result = await response.json()
                        try:
                            return True, result["data"]["outputs"]["answer"], result["data"]["total_tokens"]
                        except Exception as e:
                            self._logger.error(
                                f"call_llm_with_json 返回的数据无法解析(data->outputs->answer): {result}")
                            return False, [], 0
                    else:
                        self._logger.error(
                            f"底层大模型请求失败，状态码={response.status}, api_key={api_key}，响应内容: {await response.text()}")
                        return False, [], 0
            except asyncio.TimeoutError:
                self._logger.warning(f"call_llm timed out after {TIMEOUT_LLM} seconds")
                return False, [], 0

    def call_llm_sync(self, user_prompt, api_key="app-i3y8JTgXhIrU4QPKAasdwVal"):
        """同步版本的call_llm方法"""
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "inputs": {"user_prompt": user_prompt},
            "response_mode": "blocking",
            "user": f"chat_request_id-{self._chat_request_id}",
        }

        try:
            response = requests.post(MIFY_WORKFLOW_URL, headers=headers, data=json.dumps(body), timeout=TIMEOUT_LLM)
            if response.status_code == 200:
                result = response.json()
                try:
                    return True, result["data"]["outputs"]["answer"], result["data"]["total_tokens"]
                except Exception as e:
                    if self._logger:
                        self._logger.error(f"call_llm_sync 返回的数据无法解析(data->outputs->answer): {result}")
                    return False, [], 0
            else:
                if self._logger:
                    self._logger.error(
                        f"底层大模型请求失败，状态码={response.status_code}, api_key={api_key}，响应内容: {response.text}")
                return False, [], 0
        except requests.exceptions.Timeout:
            if self._logger:
                self._logger.warning(f"call_llm_sync timed out after {TIMEOUT_LLM} seconds")
            return False, [], 0
        except Exception as e:
            if self._logger:
                self._logger.error(f"call_llm_sync 请求异常: {str(e)}")
            return False, [], 0


if __name__ == '__main__':
    model_manager = ModelManager()
    print(model_manager.call_llm_sync("hi", "app-i3y8JTgXhIrU4QPKAasdwVal"))
