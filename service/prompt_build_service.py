from logging import Logger
from core.schema.chat_request import ChatRequest
from core.schema.knowledge_source import KnowledgeSource
from service.base_service import BaseService
from util.common_util import is_empty, not_empty


class PromptBuildService(BaseService):
    def __init__(
            self,
            logger: Logger,
            request_receive_time,
            item_param_config_dict,
            model_manager,
            doc_dataset_id_dict,
            item_name_intro_dict
    ):
        super().__init__(logger, None, None, request_receive_time)
        self._logger: Logger = logger
        self._doc_dataset_id_dict = doc_dataset_id_dict
        self._model_manager = model_manager
        self._item_name_intro_dict = item_name_intro_dict
        self._item_name_param_dict = item_param_config_dict

    def build_free_question_reject_prompt(self, chat_request: ChatRequest, answer_intent):
        item_name = "\n产品：" + chat_request.chat_history[-1].messages[0].selected_item.item_name if chat_request.chat_history[-1].messages[0].selected_item else ""
        prompt = f"""
# 角色设定
您是小米自研的促销员 AI Copilot，您的职责是帮助促销员解答关于手机相关问题。

# 任务
下面是用户提问都是超出你能力范围的问题或闲聊对话或者意图不明的输入，请参考下面回复示例灵活回复。

#回复示例：
用户意图=售前售后咨询
用户：有折扣吗？
回复：商品售价、售后服务等请以当地小米销售渠道官方信息为准。
用户意图=闲聊对话
用户：你好。
回复：您好，我是小米自研的AI助手，我擅长解答小米手机相关问题，您想咨询什么？
用户：你真笨。
回复：抱歉，给你带来麻烦了。
用户：谢谢
回复：不客气，有问题随时问我。
用户意图=时效性问题
用户：苹果哪款手机卖得最好？
回复：这貌似超出我的能力范围，目前我只擅长解答小米手机相关问题。
用户意图=其它意图
用户：给我讲个笑话
回复：这貌似超出我的能力范围，目前我只擅长解答小米手机相关问题。
用户意图=模糊意图
用户：那个
回复：我没明白您的意思，能把你的问题说得清楚一点吗？

# 输入数据
意图：{answer_intent}{item_name}
用户：{chat_request.ending_message}

# 输出要求
- 用印尼语回复
- 回答请控制在一两句话内
- 直接输出你的回复内容，不要复述用户的问题，或输出思考过程。
- 拒绝虚构参数，不确定时提示"Konfirmasi untuk Anda segera"
- 数字信息用【】突出显示
- 保持口语化，避免技术术语堆砌
"""     
        self._logger.info(f"prompt={prompt}")
        return prompt

    def build_free_question_prompt(self, chat_request: ChatRequest):
        chat_ending_message = chat_request.ending_message
        item_name = "\促销员选定的产品：" + chat_request.chat_history[-1].messages[0].selected_item.item_name if chat_request.chat_history[-1].messages[0].selected_item else ""
        prompt = f"""
# 角色设定
你是一名印尼区智能销售助手，专为印尼地区3C数码线下门店促销员提供实时支持。具备以下核心能力：
1. 产品专家：熟记全品类参数（手机/电脑/配件等），能对比竞品差异
2. 销售教练：提供FABE销售法话术、异议处理方案
3. 数据中枢：实时同步库存/促销/会员信息

# 输入数据
促销员输入：{chat_ending_message}{item_name}

# 处理逻辑
1. 语义理解：识别当前销售意图阶段（需求挖掘→产品推荐→异议处理→成交促成）
2. 信息检索：
   - 产品库：型号/参数/库存
   - 促销库：满减/赠品/分期政策
   - 案例库：典型客户应对方案
3. 动态响应：
   a. 即时数据优先（如库存状态）

# 输出要求
- 用印尼语回复
- 回答请控制在一两句话内
- 拒绝虚构参数，不确定时提示"Konfirmasi untuk Anda segera"
- 数字信息用【】突出显示
- 保持口语化，避免技术术语堆砌
"""
        self._logger.info(f"prompt={prompt}")
        return prompt

    # second_tag_map_dict is sub dict of SECOND_TAG_DICT
    def build_prompt(self, chat_request: ChatRequest, knowledge_dict):
        # get ending round from chat_history
        item_name = chat_request.item_name
        knowledge_doc_all = knowledge_dict.get(KnowledgeSource.DOC)
        knowledge_doc = list()
        knowledge_faq = list()
        if not_empty(knowledge_doc_all):
            for data_dict in knowledge_doc_all:
                line = data_dict["content"]
                score = data_dict["score"]
                if is_empty(line):
                    continue

                split_data = line.split('\n')
                if is_empty(split_data):
                    continue

                stripped = "\n".join(split_data[1:-1])
                if split_data[0] == "<FAQ>":
                    knowledge_faq.append((score, stripped))
                    continue

                if split_data[0] == "<SALE TOOLS>":
                    knowledge_doc.append((score, stripped))
                    continue

        knowledge_xml_list = list()
        high_priority_list = list()
        if not_empty(knowledge_doc):
            knowledge_doc.sort(key=lambda x: x[0], reverse=True)
            self._logger.debug(f"knowledge_doc={len(knowledge_doc)}")
            final_knowledge_doc = [element[1] for element in knowledge_doc]
            raw_content = final_knowledge_doc[0]
            knowledge_xml_list.append(f"<materi pelatihan>\n{raw_content}\n</materi pelatihan>")
        if not_empty(knowledge_faq):
            knowledge_faq.sort(key=lambda x: x[0], reverse=True)
            self._logger.debug(f"knowledge_faq={len(knowledge_faq)}")
            final_knowledge_faq = [element[1] for element in knowledge_faq]
            raw_content = "\n".join(final_knowledge_faq[:3])
            knowledge_xml_list.append(f"<masalah umum>\n{raw_content}\n</masalah umum>")
        knowledge_intro = self._item_name_intro_dict.get(chat_request.item_name_normalized)
        if is_empty(knowledge_intro):
            self._logger.warning(f"构建回答提示词时 {chat_request.item_name_normalized} 没有找到米网介绍文案")
        else:
            raw_content = "\n".join(knowledge_intro)
            knowledge_xml_list.append(f"<bahan promosi>\n{raw_content}\n</bahan promosi>")
            high_priority_list.append("<bahan promosi>")
        knowledge_param = self._item_name_param_dict.get(chat_request.item_name_normalized)
        if is_empty(knowledge_param):
            self._logger.warning(f"构建回答提示词时 {chat_request.item_name_normalized} 没有找到米网参数")
        else:
            raw_content = "\n".join(knowledge_param)
            knowledge_xml_list.append(f"<informasi parameter>\n{raw_content}\n</informasi parameter>")
            high_priority_list.append("<informasi parameter>")
        if is_empty(knowledge_xml_list):
            return None

        priority_str = ""
        if not_empty(high_priority_list):
            priority_str = f"(优先使用{'、'.join(high_priority_list)}中的信息)"
        knowledge_all_str = "\n".join(knowledge_xml_list)
        # 最后做了问题增强：对于 {item_name} 手机，我想问一下，{ending_message}
        prompt = f"""你是一个小米手机知识专家，以下是关于手机 {item_name} 的信息：
<informasi seluler>
{knowledge_all_str}
</informasi seluler>
请根据以上信息{priority_str}用印尼语准确地回答用户问题（不要输出 xml 标签信息），并将结果用 markdown 格式返回（比如对关键信息进行加粗等），回答请控制在一两句话内；若用户问的是产品缺陷相关的问题，请在回复缺陷事实的同时加以解释，以挽留用户；回复中不要涉及时效性相关的信息，例如：‘最新’、‘最近’等字眼；若用户问题仅仅包含机型，则生成该机型基本信息；若用户问题涉及到FABE相关字眼，请根据FABE营销法则描述产品亮点
用户问题为：Untuk ponsel {item_name} saya ingin bertanya, {chat_request.ending_message}
回答：
"""
        self._logger.info(f"prompt len={len(prompt)}")
        self._logger.info(f"prompt={prompt}")
        return prompt

    @staticmethod
    def enhance_retrieval_query(query, second_tag_map_dict):
        if is_empty(second_tag_map_dict):
            return query

        postfix = f"melibatkan aspek {', '.join(second_tag_map_dict.values())}"
        return f"{query}({postfix})"

    async def retrieve_doc_knowledge(self, query, second_tag_map_dict, item_name_normalized):
        enhanced_query = self.enhance_retrieval_query(query, second_tag_map_dict)
        self._logger.debug(f"用来召回知识的 query={enhanced_query}")
        # different spu use different dataset_id
        if item_name_normalized not in self._doc_dataset_id_dict:
            self._logger.warning(f"没有找到{item_name_normalized}对应的知识（dataset id 缺失）")
            return list()

        doc_dataset_id = self._doc_dataset_id_dict[item_name_normalized]
        return await self._model_manager.retrieve_knowledge_with_score(enhanced_query, doc_dataset_id)
