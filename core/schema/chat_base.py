from enum import IntEnum, Enum
from typing import Optional, List

from pydantic import BaseModel

from core.schema.constant import UNDEFINED


class Item(BaseModel):
    item_id: str
    item_name: str
    category_id: Optional[str]
    category_name: Optional[str]

    def __repr__(self):
        return self.item_name

    def __str__(self):
        return self.item_name


class MessageType(IntEnum):
    # 未知 (Unknown)
    UNKNOWN = 0
    # 正常文本信息 (Normal text message)
    TEXT = 1
    # 非手机问题拒答 (Non-phone question rejection)
    NOT_IN_SCOPE = 2
    # 机型不一致拒答 (Model mismatch rejection)
    NOT_MATCH_ITEM = 3
    # 没相关知识拒答 (No relevant knowledge rejection)
    NO_KNOWLEDGE = 4
    # 销售信息拒答 (Sales information rejection)
    DYNAMIC_CHANGE = 5
    # 机型不支持拒答 (Model not supported rejection)
    NOT_SUPPORTED_ITEM = 6
    # 用户确认商品 (User confirms product)
    ITEM_CONFIRM = 7
    # 双机对比 (Device comparison)
    ITEM_COMPARE = 8
    # 给出候选项 (Provide candidates)
    ITEM_CANDIDATE = 9
    # 双机对比机型识别模糊 (Device comparison model recognition is ambiguous)
    ITEM_COMPARE_UNCERTAIN = 10
    # 未识别到机型 (Model not recognized)
    ITEM_UNRECOGNIZED = 11
    # 非 3c 数码产品的自由类问题的拒答 (Rejection of free questions about non-3c digital products)
    FREE_FAQ_REJECT = 12
    # 双机对比机型不支持非小米机型之间的对比 (Device comparison does not support comparison between non-Xiaomi models)
    NO_XIAOMI_ITEM = 13
    # 自由问答中可以回答的部分 (Parts that can be answered in free Q&A)
    FREE_FAQ_ANSWER = 14

    @property
    def description(self) -> str:
        """获取当前枚举值的解释"""
        # 每个枚举值对应的解释
        descriptions = {
            MessageType.UNKNOWN: "未知",
            MessageType.TEXT: "正常文本信息",
            MessageType.NOT_IN_SCOPE: "非手机问题拒答",
            MessageType.NOT_MATCH_ITEM: "机型不一致拒答",
            MessageType.NO_KNOWLEDGE: "没相关知识拒答",
            MessageType.DYNAMIC_CHANGE: "销售信息拒答",
            MessageType.NOT_SUPPORTED_ITEM: "机型不支持拒答",
            MessageType.ITEM_CONFIRM: "用户确认商品",
            MessageType.ITEM_COMPARE: "双机对比",
            MessageType.ITEM_CANDIDATE: "给出候选项",
            MessageType.ITEM_COMPARE_UNCERTAIN: "双机对比机型识别模糊",
            MessageType.ITEM_UNRECOGNIZED: "未识别到机型",
            MessageType.FREE_FAQ_REJECT: "非 3c 数码产品的自由类问题的拒答",
            MessageType.NO_XIAOMI_ITEM: "双机对比机型不支持非小米机型之间的对比",
            MessageType.FREE_FAQ_ANSWER: "自由问答中可以回答的部分"
        }
        return descriptions.get(self, UNDEFINED)

    def __repr__(self):
        return str(self.value)

    @classmethod
    def to_str_dict(cls):
        """返回一个包含所有枚举值及其解释的字典"""
        return {int(msg_type): msg_type.description for msg_type in cls}


class Message(BaseModel):
    type: Optional[MessageType] = MessageType.TEXT
    content: Optional[str]
    selected_item: Optional[Item] = None
    item_list: Optional[List[Item]] = None


class IntentType(Enum):
    # 手机产品相关问答
    QUESTION_ANSWER = ("规格参数", "软件使用", "卖点咨询", "缺点咨询")
    # 双机对比问答
    ITEM_COMPARE = ("双机对比",)
    # 自由问答中需要拒答的部分
    FREE_QUESTION_REJECT = ("售前售后咨询", "时效性问题", "闲聊对话", "其他意图", "模糊意图")
    # 自由问答中可以回答的部分
    FREE_QUESTION = ("数码知识",)

    def as_set(self):
        return set(self.value)
