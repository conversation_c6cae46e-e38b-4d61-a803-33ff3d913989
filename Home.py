import streamlit as st

# Set page configuration - 必须是第一个 Streamlit 命令
st.set_page_config(
    page_title="国际促销员 Copilot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Display welcome message
st.title("国际促销员 Copilot")
st.markdown("""
## 欢迎使用国际促销员 Copilot

请从侧边栏或使用以下链接选择功能：
""")

col1, col2 = st.columns(2)

with col1:
    st.markdown("### 功能页面")
    st.markdown("- [单个请求](chat) - 进行单个对话请求（430版）")
    st.markdown("- [历史对话](history_chat) - 支持完整历史对话和选择机型（530版）")
    st.markdown("- [批量处理](regression) - 批量处理请求")
    st.markdown("- [缺陷验证](bad) - 验证已知缺陷")

with col2:
    st.markdown("### 管理页面")
    st.markdown("- [运维](ops) - 系统运维功能")
    st.markdown("- [标注](label) - 数据标注功能")

# 添加一些简单的说明
st.info("👈 请从左侧边栏选择功能，或者点击上方链接直接访问相应页面")

# 添加一个图片或者其他内容
st.success("✅ 系统已准备就绪")
