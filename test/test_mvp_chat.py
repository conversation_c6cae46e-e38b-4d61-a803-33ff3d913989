import unittest

from core.schema.chat_base import MessageType
from direct_chat import direct_chat_to_copilot_from_file
from util.common_util import pprint, assert_eq, assert_true


class TestMvpChat(unittest.TestCase):

    def test0(self):
        path = "test/test_data/430/0_屏幕多大.json"
        result = direct_chat_to_copilot_from_file(path, 0)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        assert_true("6,67" in result.data.text)

    def test1(self):
        path = "test/test_data/430/1_手机重量.json"
        result = direct_chat_to_copilot_from_file(path, 1)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        assert_true("192" in result.data.text)

    def test2(self):
        path = "test/test_data/430/2_你是谁.json"
        result = direct_chat_to_copilot_from_file(path, 2)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        assert_true("asisten digital" in result.data.text)
        assert_true("Redmi 12C" in result.data.text)

    def test3(self):
        path = "test/test_data/430/3_内存多少.json"
        result = direct_chat_to_copilot_from_file(path, 3)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        assert_true("8 GB" in result.data.text)


if __name__ == "__main__":
    unittest.main()
