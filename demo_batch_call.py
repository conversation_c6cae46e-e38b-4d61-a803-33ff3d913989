import asyncio
import aiohttp
import streamlit as st
from pathlib import Path
from scripts.val_batch_call import process_batch, save_dict_to_excel, load_json_info_for_process

# 创建临时目录
TMP_DIR = Path("./tmp")
TMP_DIR.mkdir(exist_ok=True)

st.title("批量推理与评测工具")

# 确保上传的文件路径和处理结果保存在 session_state 中
if "uploaded_file_path" not in st.session_state:
    st.session_state["uploaded_file_path"] = './tmp/评测集-test.xlsx'
if "all_results" not in st.session_state:
    st.session_state["all_results"] = None
if "val_res" not in st.session_state:
    st.session_state["val_res"] = None

st.info(f"默认使用的文件: ./tmp/评测集-test.xlsx, 可直接运行测试")

# 上传文件
uploaded_file = st.file_uploader("上传要处理的文件 (Excel 格式)", type=["xlsx"])
if uploaded_file:
    input_file_path = TMP_DIR / uploaded_file.name
    with open(input_file_path, "wb") as f:
        f.write(uploaded_file.getbuffer())
    st.session_state["uploaded_file_path"] = input_file_path
    st.success(f"文件已上传: {input_file_path}, 当前正在使用的文件: {st.session_state['uploaded_file_path']}")

# 配置参数
batch_size = st.number_input("每次并发请求的数量 (batch_size)", min_value=1, value=10)
val_score_flag = st.checkbox("是否评测分数 (val_score_flag)", value=True)
product_str = st.text_input("产品名称列名 (product_str)必须提供", value="SPU", )
query_str = st.text_input("问题列名 (query_str)必须提供", value="Question")
ref_ans = st.text_input("参考答案列名 (ref_ans)非必须，不提供不进行打分", value="Answer-final")

# 输出文件路径
output_file_path = TMP_DIR / "output_file.xlsx"
val_res_file_path = TMP_DIR / "val_res_file.xlsx"

# 初始化运行状态
if "is_running" not in st.session_state:
    st.session_state["is_running"] = False

def disable_run_button():
    st.session_state["is_running"] = True
# 运行按钮
if st.button("运行", disabled=st.session_state["is_running"], on_click=disable_run_button):
    if not st.session_state["uploaded_file_path"]:
        st.error("请先上传文件！")
    else:
        st.session_state["is_running"] = True
        st.info("开始处理，请稍候...")

        qa_list, columns_to_read, extra_col_names = load_json_info_for_process(
            st.session_state["uploaded_file_path"], product_str, query_str, ref_ans
        )
        total_batches = (len(qa_list) + batch_size - 1) // batch_size
        all_results = []

        # 初始化进度条
        progress_bar = st.progress(0)

        async def run_batches():
            async with aiohttp.ClientSession() as session:
                for batch_num in range(total_batches):
                    start_idx = batch_num * batch_size
                    end_idx = min((batch_num + 1) * batch_size, len(qa_list))
                    batch_qa_list = qa_list[start_idx:end_idx]
                    batch_results = await process_batch(session, batch_qa_list, "preview", val_score_flag, batch_num + 1, extra_col_names=extra_col_names)
                    all_results.extend(batch_results)
                    save_dict_to_excel(all_results, output_file_path)
                    # 更新进度条
                    progress_bar.progress((batch_num + 1) / total_batches, text=f'正在处理第 {batch_num + 1} 批，共 {total_batches} 批')
            st.session_state["all_results"] = all_results
            st.session_state["is_running"] = False

        asyncio.run(run_batches())

        st.info(f"生成评测结果...")
        count_first_token_time_count = sum(1 for result in all_results if result.get("first_token_time") != "")
        count_first_token_time = sum(
            result.get("first_token_time", 0) for result in all_results if result.get("first_token_time") != "")
        count_first_token_time_average = count_first_token_time / count_first_token_time_count if count_first_token_time_count > 0 else 0

        count_infer_inner_time_count = sum(1 for result in all_results if
                                           result.get("first_token_time") != "" and result.get(
                                               "infer_time_inner") != "")
        count_infer_inner_time = sum(result.get("infer_time_inner", 0) for result in all_results if
                                     result.get("first_token_time") != "" and result.get("infer_time_inner") != "")
        count_infer_inner_time_average = count_infer_inner_time / count_infer_inner_time_count if count_first_token_time_count > 0 else 0

        count_infer_feel_time_count = sum(
            1 for result in all_results if result.get("first_token_time") != "" and result.get("推理时间") != "")
        count_infer_feel_time = sum(result.get("推理时间", 0) for result in all_results if
                                    result.get("first_token_time") != "" and result.get("推理时间") != "")
        count_infer_feel_time_average = count_infer_feel_time / count_infer_feel_time_count if count_first_token_time_count > 0 else 0

        # 统计 score 等于 2 的个数
        count_score_2 = sum(1 for result in all_results if result.get("score") == 2)
        # 计算准确率
        accuracy = count_score_2 / len(all_results) if all_results else 0
        infer_success_rate = count_first_token_time_count / len(qa_list) if len(qa_list) > 0 else 0

        val_res = []
        val_res.append({
            "AI打分准确率": f"{accuracy:.2%}",
            "平均首Token时间": count_first_token_time_average,
            "平均infer时间": count_infer_inner_time_average,
            "体感平均infer时间": count_infer_feel_time_average,
            "infer_success_rate": f"{infer_success_rate:.2%}",
            "infer_success": count_first_token_time_count,
            "infer_counts": len(qa_list),
            "infer_fail": len(qa_list) - count_first_token_time_count,
        })
        save_dict_to_excel(val_res, val_res_file_path)
        st.session_state["val_res"] = val_res
        st.success("处理完成！")

# 删除已上传文件按钮
if st.button("删除已上传文件"):
    if st.session_state["uploaded_file_path"] and Path(st.session_state["uploaded_file_path"]).exists():
        if not st.session_state["uploaded_file_path"] == './tmp/评测集-test.xlsx':
            Path(st.session_state["uploaded_file_path"]).unlink()  # 删除文件
            st.session_state["uploaded_file_path"] = './tmp/评测集-test.xlsx'
        st.success("已删除上传的文件及相关状态！")
    else:
        st.warning("没有找到已上传的文件或文件已被删除！")

# 提供推理结果文件下载
if st.session_state["all_results"]:
    with open(output_file_path, "rb") as f:
        output_file_data = f.read()
    st.download_button(
        label="下载推理结果文件",
        data=output_file_data,
        file_name=output_file_path.name,
        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    )

# 提供评测结果文件下载
if st.session_state["val_res"]:
    with open(val_res_file_path, "rb") as f:
        val_res_file_data = f.read()
    st.download_button(
        label="下载评测结果文件",
        data=val_res_file_data,
        file_name=val_res_file_path.name,
        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    )