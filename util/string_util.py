def text_to_xml(text_lines):
    xml = ""
    for line in text_lines:
        parts = line.split(": ", 1)
        if len(parts) == 2:
            tag = parts[0].strip().replace(" ", "_")
            content = parts[1].strip()
            xml += f"<{tag}>{content}</{tag}>\n"
        else:
            tag = line.strip().replace(" ", "_")
            xml += f"<{tag}></{tag}>\n"
    # 去掉最后的换行符
    return xml.strip()  


def normalize_item_name(raw_name):
    raw_name = raw_name.strip()
    # 去掉字符串中的所有空格
    no_spaces = raw_name.replace(" ", "")
    # 将+换成 plus
    replace_plus = no_spaces.replace("+", "plus")
    # 将所有字符转换为小写
    lower_case = replace_plus.lower()
    return lower_case

if __name__ == '__main__':
    # s = "model: Redmi Note 13 EU\npertanyaan: Apa nilai jual utama dari Redmi Note 13?\nMenjawab: 1. Redmi Note 13 hadir dengan mesin boost liar dan prosesor Qualcomm Snapdragon® 685 terkemuka di industri yang menghadirkan pengalaman yang sehalus sutra;2. Layar menggunakan layar AMOLED dengan resolusi 2400*1080, bidang pandang jernih 395 PPI, dan kecepatan refresh hingga 120Hz untuk memastikan gambar yang halus dan menarik dan kecepatan pengambilan sampel sentuh 240Hz mengarah pada pengalaman sentuh yang lebih responsif dan akurat;3. Fotografi 108 juta piksel, analisis kualitas gambar ultra jernih, dengan 8 juta sudut ultra lebar, cakupan penuh orang, objek, dan pemandangan;4. Baterai besar 5000mAh, memastikan daya terus menerus;5. Empat pilihan warna yang indah, yang sesuai dengan gaya Anda untuk setiap kesempatan.Informasi produk"
    # print(text_to_xml(s.split("\n")))
    print(normalize_item_name("Redmi Note 14 Pro+ 5G"))
