import json
import re
import time

from core.schema.chat_response import ChatResponse


def assert_eq(expected, actual):
    if expected != actual:
        raise RuntimeError(f"expected={expected}, actual={actual}")


def assert_not_eq(expected, actual):
    if expected == actual:
        raise RuntimeError(f"expected={expected}, actual={actual}")


def assert_true(actual):
    if not actual:
        raise RuntimeError(f"expected True, actual={actual}")


def chunk_list(lst, n):
    return [lst[i:i + n] for i in range(0, len(lst), n)]


def is_empty(item):
    return item is None or len(item) == 0


def not_empty(item):
    return not is_empty(item)


def get_cur_millis():
    return int(time.time() * 1000)


def decode_sse(raw_event_str):
    if not raw_event_str.startswith("data:"):
        return dict()

    return json.loads(raw_event_str[6:])


def encode_sse(chat_response: ChatResponse):
    data = chat_response.to_dict()
    return f"data: {json.dumps(data)}\n\n"


def get_ending_message(chat_request):
    return chat_request.chat_history[-1].messages[-1].content


def may_have_bad_markdown(text):
    if is_empty(text):
        return False

    markdown_pattern = r'(markdown|Markdown|MarkDown)'
    return re.search(markdown_pattern, text) or text.startswith("```")


def pprint(data):
    print("---")
    if isinstance(data, dict):
        print(json.dumps(data, indent=4, ensure_ascii=False))
    elif hasattr(data, 'to_dict') and callable(getattr(data, 'to_dict')):
        print(json.dumps(data.to_dict(), indent=4, ensure_ascii=False))
    else:
        print(data)
    print("---")


if __name__ == "__main__":
    raw_event_str = 'data: {"event": "text_chunk", "workflow_run_id": "20afa149-8bab-4574-8304-b0962b326b0b", "task_id": "bf7ad761-4836-48f4-a2a0-b9f4e128a3c0", "data": {"text": " assist", "from_variable_selector": ["1743391162492", "text"]}}\n\n'
    print(encode_sse(decode_sse(raw_event_str)))
