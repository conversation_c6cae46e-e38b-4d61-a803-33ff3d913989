#!/usr/bin/env python3
"""
测试额外列保留功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scripts.val_batch_call import load_json_info_for_process

def test_extra_columns_functionality():
    """测试额外列保留功能"""
    
    # 测试文件路径
    test_file = "test/test_data/国际Copilot AI问答评测集v0.1.xlsx"
    
    print("=== 测试额外列保留功能 ===")
    print(f"测试文件: {test_file}")
    
    # 测试1: 不指定额外列
    print("\n测试1: 不指定额外列")
    qa_list1, columns1, extra_cols1 = load_json_info_for_process(
        test_file, "SPU", "问题-印尼", "人工答案-印尼", ""
    )
    print(f"加载的列: {columns1}")
    print(f"额外列: {extra_cols1}")
    print(f"数据形状: {qa_list1.shape}")
    
    # 测试2: 指定一个额外列
    print("\n测试2: 指定一个额外列")
    qa_list2, columns2, extra_cols2 = load_json_info_for_process(
        test_file, "SPU", "问题-印尼", "人工答案-印尼", "问题-中文"
    )
    print(f"加载的列: {columns2}")
    print(f"额外列: {extra_cols2}")
    print(f"数据形状: {qa_list2.shape}")
    
    # 测试3: 指定多个额外列
    print("\n测试3: 指定多个额外列")
    qa_list3, columns3, extra_cols3 = load_json_info_for_process(
        test_file, "SPU", "问题-印尼", "人工答案-印尼", "问题-中文,人工答案-中文,Source"
    )
    print(f"加载的列: {columns3}")
    print(f"额外列: {extra_cols3}")
    print(f"数据形状: {qa_list3.shape}")
    
    # 测试4: 指定不存在的列
    print("\n测试4: 指定不存在的列")
    qa_list4, columns4, extra_cols4 = load_json_info_for_process(
        test_file, "SPU", "问题-印尼", "人工答案-印尼", "不存在的列,问题-中文,另一个不存在的列"
    )
    print(f"加载的列: {columns4}")
    print(f"额外列: {extra_cols4}")
    print(f"数据形状: {qa_list4.shape}")
    
    # 验证数据内容
    print("\n=== 验证数据内容 ===")
    print("第一行数据 (测试3):")
    for i, col in enumerate(columns3):
        print(f"  {col}: {qa_list3[0][i]}")
    
    print("\n功能测试完成！")

if __name__ == "__main__":
    test_extra_columns_functionality()
